"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { CampaignType } from "@prisma/client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

const campaignSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  type: z.nativeEnum(CampaignType),
  emailTemplateId: z.string().min(1, "Template é obrigatório"),
});

type CampaignFormData = z.infer<typeof campaignSchema>;

interface EmailTemplate {
  id: string;
  title: string;
}

interface CampaignFormProps {
  initialData?: Partial<CampaignFormData>;
  isEditing?: boolean;
  campaignId?: string;
  templates: EmailTemplate[];
}

export function CampaignForm({
  initialData,
  isEditing,
  campaignId,
  templates = [], // Valor padrão como array vazio
}: CampaignFormProps) {
  const router = useRouter();

  const form = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: initialData?.name || "",
      type: initialData?.type || "PATIENT",
      emailTemplateId: initialData?.emailTemplateId || "",
    },
  });

  const onSubmit = async (data: CampaignFormData) => {
    try {
      const url = isEditing
        ? `/api/admin/campaigns/${campaignId}`
        : "/api/admin/campaigns";
      const method = isEditing ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Erro ao salvar campanha");
      }

      toast.success(
        isEditing
          ? "Campanha atualizada com sucesso"
          : "Campanha criada com sucesso"
      );
      router.push("/admin/campanhas");
      router.refresh();
    } catch (error) {
      toast.error("Erro ao salvar campanha");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel>Tipo</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="PATIENT">Paciente</SelectItem>
                  <SelectItem value="PROFESSIONAL">Profissional</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="emailTemplateId"
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel>Template</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Selecione o template" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Array.isArray(templates) &&
                    templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.title}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between gap-4">
          <Button
            type="button"
            variant="outline"
            className="cursor-pointer"
            onClick={() => router.push("/admin/campanhas")}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            className="cursor-pointer"
            disabled={form.formState.isSubmitting}
          >
            {form.formState.isSubmitting
              ? "Salvando..."
              : isEditing
              ? "Salvar Alterações"
              : "Criar Campanha"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
