"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { urgencySchema, UrgencyFormData } from "@/lib/types/urgency";

interface UrgencyFormProps {
  initialData?: Partial<UrgencyFormData>;
  isEditing?: boolean;
  onSubmit: (data: UrgencyFormData) => Promise<void>;
}

export function UrgencyForm({
  initialData,
  isEditing,
  onSubmit,
}: UrgencyFormProps) {
  const router = useRouter();

  const form = useForm<UrgencyFormData>({
    resolver: zodResolver(urgencySchema),
    defaultValues: {
      name: initialData?.name || "",
    },
  });

  const handleSubmit = async (data: UrgencyFormData) => {
    try {
      await onSubmit(data);
      toast.success(
        isEditing
          ? "Urgência atualizada com sucesso"
          : "Urgência criada com sucesso"
      );
      router.push("/admin/urgencias");
      router.refresh();
    } catch (error) {
      toast.error("Erro ao salvar urgência");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/admin/urgencias")}
          >
            Cancelar
          </Button>
          <Button type="submit">{isEditing ? "Atualizar" : "Salvar"}</Button>
        </div>
      </form>
    </Form>
  );
}
