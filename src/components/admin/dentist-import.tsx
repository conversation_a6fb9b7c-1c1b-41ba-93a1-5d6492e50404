"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { toast } from "sonner";
import * as XLSX from "xlsx";

interface DentistImportData {
  name: string;
  email: string;
  whatsapp: string;
  password: string;
}

export function DentistImport({ onSuccess }: { onSuccess: () => void }) {
  const [isLoading, setIsLoading] = useState(false);

  const processExcel = async (file: File) => {
    try {
      setIsLoading(true);
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json<DentistImportData>(worksheet, {
        header: ["name", "email", "whatsapp", "password"],
        range: 1, // Skip header row
      });

      const response = await fetch("/api/admin/dentists/import", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ dentists: jsonData }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Erro ao importar dentistas");
      }

      const result = await response.json();
      toast.success(`${result.imported} dentistas importados com sucesso`);
      if (result.skipped > 0) {
        toast.info(`${result.skipped} dentistas ignorados (email duplicado)`);
      }
      onSuccess();
    } catch (error) {
      console.error("Erro na importação:", error);
      toast.error("Erro ao importar arquivo");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith(".xlsx")) {
      toast.error("Por favor, selecione um arquivo XLSX");
      return;
    }

    processExcel(file);
  };

  return (
    <div>
      <Button
        variant="outline"
        disabled={isLoading}
        onClick={() => document.getElementById("xlsxInput")?.click()}
      >
        <Upload className="h-4 w-4" />
        <span className="hidden md:inline ml-2">
          {isLoading ? "Importando..." : "Importar XLSX"}
        </span>
      </Button>
      <input
        id="xlsxInput"
        type="file"
        accept=".xlsx"
        className="hidden"
        onChange={handleFileChange}
      />
    </div>
  );
}
