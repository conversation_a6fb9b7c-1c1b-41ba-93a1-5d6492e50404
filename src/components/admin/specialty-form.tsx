"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { specialtySchema, SpecialtyFormData } from "@/lib/types/specialty";

interface SpecialtyFormProps {
  initialData?: Partial<SpecialtyFormData>;
  isEditing?: boolean;
  onSubmit: (data: SpecialtyFormData) => Promise<void>;
}

export function SpecialtyForm({
  initialData,
  isEditing,
  onSubmit,
}: SpecialtyFormProps) {
  const router = useRouter();

  const form = useForm<SpecialtyFormData>({
    resolver: zodResolver(specialtySchema),
    defaultValues: {
      name: initialData?.name || "",
    },
  });

  const handleSubmit = async (data: SpecialtyFormData) => {
    try {
      await onSubmit(data);
      toast.success(
        isEditing
          ? "Especialidade atualizada com sucesso"
          : "Especialidade criada com sucesso"
      );
      router.push("/admin/especialidades");
      router.refresh();
    } catch (error) {
      toast.error("Erro ao salvar especialidade");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/admin/especialidades")}
          >
            Cancelar
          </Button>
          <Button type="submit">{isEditing ? "Atualizar" : "Salvar"}</Button>
        </div>
      </form>
    </Form>
  );
}
