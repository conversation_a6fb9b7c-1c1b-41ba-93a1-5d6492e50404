"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

const templateSchema = z.object({
  key: z.enum([
    "WELCOME_EMAIL",
    "PASSWORD_RESET",
    "APPOINTMENT_CONFIRMATION",
    "APPOINTMENT_REMINDER",
    "APPOINTMENT_CANCELLATION",
  ]),
  title: z.string().min(1, "Título é obrigatório"),
  template: z.string().min(1, "Template é obrigatório"),
});

type TemplateFormData = z.infer<typeof templateSchema>;

interface TemplateFormProps {
  initialData?: Partial<TemplateFormData>;
  isEditing?: boolean;
  onSubmit: (data: TemplateFormData) => Promise<void>;
}

export function TemplateForm({
  initialData,
  isEditing,
  onSubmit,
}: TemplateFormProps) {
  const router = useRouter();

  const form = useForm<TemplateFormData>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      key: initialData?.key || "WELCOME_EMAIL",
      title: initialData?.title || "",
      template: initialData?.template || "",
    },
  });

  const handleSubmit = async (data: TemplateFormData) => {
    try {
      await onSubmit(data);
      toast.success(
        isEditing
          ? "Template atualizado com sucesso"
          : "Template criado com sucesso"
      );
      router.push("/admin/templates");
      router.refresh();
    } catch (error) {
      toast.error("Erro ao salvar template");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="key"
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel>Tipo de Template</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="WELCOME_EMAIL">Boas-vindas</SelectItem>
                  <SelectItem value="PASSWORD_RESET">
                    Redefinição de Senha
                  </SelectItem>
                  <SelectItem value="APPOINTMENT_CONFIRMATION">
                    Confirmação de Consulta
                  </SelectItem>
                  <SelectItem value="APPOINTMENT_REMINDER">
                    Lembrete de Consulta
                  </SelectItem>
                  <SelectItem value="APPOINTMENT_CANCELLATION">
                    Cancelamento de Consulta
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Título</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="template"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Template</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  rows={10}
                  placeholder="Digite o template do email aqui..."
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/admin/templates")}
          >
            Cancelar
          </Button>
          <Button type="submit">
            {isEditing ? "Salvar alterações" : "Criar template"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
