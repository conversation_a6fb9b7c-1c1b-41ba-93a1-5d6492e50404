"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

const baseSchema = {
  firstName: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
};

const createAdminSchema = z.object({
  ...baseSchema,
  password: z.string().min(6, "Senha deve ter no mínimo 6 caracteres"),
});

const editAdminSchema = z.object({
  ...baseSchema,
  password: z
    .string()
    .min(6, "Senha deve ter no mínimo 6 caracteres")
    .optional()
    .or(z.literal("")),
});

type CreateAdminFormData = z.infer<typeof createAdminSchema>;
type EditAdminFormData = z.infer<typeof editAdminSchema>;

interface AdminFormProps {
  initialData?: Partial<EditAdminFormData>;
  isEditing?: boolean;
  adminId?: string;
}

export function AdminForm({ initialData, isEditing, adminId }: AdminFormProps) {
  const router = useRouter();

  // Definindo o tipo correto baseado no modo de edição
  type FormData = typeof isEditing extends true
    ? EditAdminFormData
    : CreateAdminFormData;

  const form = useForm<FormData>({
    resolver: zodResolver(isEditing ? editAdminSchema : createAdminSchema),
    defaultValues: {
      firstName: initialData?.firstName ?? "",
      email: initialData?.email ?? "",
      password: "", // Sempre inicialize password como string vazia
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      const url = isEditing ? `/api/admin/${adminId}` : "/api/admin";
      const method = isEditing ? "PUT" : "POST";

      // Remove senha vazia ao editar
      const submitData = { ...data };
      if (isEditing && !submitData.password) {
        delete (submitData as any).password;
      }

      const response = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Erro ao salvar administrador");
      }

      toast.success(
        isEditing
          ? "Administrador atualizado com sucesso"
          : "Administrador criado com sucesso"
      );
      router.push("/admin");
      router.refresh();
    } catch (error) {
      console.error("Erro ao salvar:", error);
      toast.error(
        error instanceof Error ? error.message : "Erro ao salvar administrador"
      );
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nome</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} type="email" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {isEditing ? "Nova Senha (opcional)" : "Senha"}
              </FormLabel>
              <FormControl>
                <Input {...field} type="password" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-4">
          <Button
            type="button"
            variant="outline"
            className="cursor-pointer"
            onClick={() => router.push("/admin")}
          >
            Cancelar
          </Button>
          <Button type="submit" className="cursor-pointer">
            {isEditing ? "Atualizar" : "Criar"} Administrador
          </Button>
        </div>
      </form>
    </Form>
  );
}
