"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";
import { loginSchema, type LoginFormData } from "@/lib/validations/auth";

export function LoginForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log("Attempting login with:", {
        email: data.email,
        passwordLength: data.password.length,
      });

      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        setError("Credenciais inválidas");
        return;
      }

      // Buscar informações do usuário após login bem-sucedido
      const userResponse = await fetch("/api/user/profile");
      const userData = await userResponse.json();

      // Redirecionar baseado no tipo de usuário
      console.log(userData.type);
      switch (userData.type) {
        case "admin":
          router.push("/admin");
          break;
        case "patient":
          router.push("/paciente/home");
          break;
        case "dentist":
          router.push("/dentista/home");
          break;
        default:
          setError("Tipo de usuário inválido");
      }
    } catch (error) {
      setError("Ocorreu um erro ao fazer login");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserTypeSelection = (type: "paciente" | "dentista") => {
    setDialogOpen(false);
    router.push(`/login/criar-conta/${type}`);
  };

  return (
    <div className="w-full max-w-md p-8 space-y-6 bg-card rounded-lg shadow-lg">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold">Login</h1>
        <p className="text-muted-foreground">
          Entre com suas credenciais para acessar
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Senha</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="••••••••" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button className="w-full cursor-pointer" type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Entrando...
              </>
            ) : (
              "Entrar"
            )}
          </Button>
        </form>
      </Form>

      <div className="space-y-2 text-center">
        <Link
          href="/login/forgot-password"
          className="text-sm text-primary hover:underline"
        >
          Esqueci minha senha
        </Link>

        <div className="text-sm">
          Não tem uma conta?{" "}
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="link" className="text-primary p-0">
                Criar conta
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogTitle className="text-center mb-4">
                Escolha o tipo de conta
              </DialogTitle>
              <div className="grid grid-cols-2 gap-4">
                <Card
                  className="p-4 cursor-pointer hover:bg-slate-50 transition-colors"
                  onClick={() => handleUserTypeSelection("paciente")}
                >
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 bg-blue-100 rounded-full mx-auto flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-blue-500"
                      >
                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                        <circle cx="12" cy="7" r="4" />
                      </svg>
                    </div>
                    <h3 className="font-semibold">Paciente</h3>
                    <p className="text-sm text-muted-foreground">
                      Criar conta como paciente
                    </p>
                  </div>
                </Card>

                <Card
                  className="p-4 cursor-pointer hover:bg-slate-50 transition-colors"
                  onClick={() => handleUserTypeSelection("dentista")}
                >
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 bg-blue-100 rounded-full mx-auto flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-blue-500"
                      >
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                        <polyline points="9 22 9 12 15 12 15 22" />
                      </svg>
                    </div>
                    <h3 className="font-semibold">Dentista</h3>
                    <p className="text-sm text-muted-foreground">
                      Criar conta como dentista
                    </p>
                  </div>
                </Card>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
