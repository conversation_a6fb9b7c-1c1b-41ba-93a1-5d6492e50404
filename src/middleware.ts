import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { NextRequestWithAuth } from "next-auth/middleware";

export default async function middleware(request: NextRequestWithAuth) {
  const token = await getToken({ req: request });

  // Se não estiver autenticado, redireciona para login
  if (!token) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Verifica rotas admin
  if (request.nextUrl.pathname.startsWith("/admin")) {
    if (token.type !== "admin") {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  // Verifica rotas paciente
  if (request.nextUrl.pathname.startsWith("/paciente")) {
    if (token.type === "admin") {
      return NextResponse.redirect(new URL("/admin", request.url));
    }
    if (token.type !== "patient") {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  // Verifica rotas dentista
  if (request.nextUrl.pathname.startsWith("/dentista")) {
    if (token.type === "admin") {
      return NextResponse.redirect(new URL("/admin", request.url));
    }
    if (token.type !== "dentist") {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/admin/:path*", "/paciente/:path*", "/dentista/:path*"],
};
