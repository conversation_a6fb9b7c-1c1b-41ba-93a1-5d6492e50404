import { Login<PERSON>ormD<PERSON>, LoginResponse } from "@/types/auth";
import { NextAuthOptions, Session } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { db } from "@/lib/db";
import { compare } from "bcryptjs";
import { loginSchema } from "@/lib/validations/auth";
import { getServerSession } from "next-auth";

interface CustomUser {
  id: string;
  type: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

interface CustomSession extends Session {
  user: CustomUser;
}

export const authOptions: NextAuthOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
    newUser: "/login/create-account",
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log("JWT Callback - Input:", { token, user });
      if (user) {
        token.id = user.id;
        token.type = (user as any).type;
      }
      console.log("JWT Callback - Output:", token);
      return token;
    },
    async session({ session, token }) {
      console.log("Session Callback - Input:", { session, token });
      if (session.user) {
        (session.user as any).id = token.id as string;
        (session.user as any).type = token.type as string;
      }
      console.log("Session Callback - Output:", session);
      return session;
    },
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          console.log("Missing credentials");
          throw new Error("Missing credentials");
        }

        try {
          // Primeiro, vamos logar a tentativa de busca
          console.log("Searching for user with email:", credentials.email);

          const user = await db.user.findUnique({
            where: {
              email: credentials.email,
            },
            select: {
              id: true,
              email: true,
              hashedPassword: true,
              type: true,
              firstName: true,
            },
          });

          // Log do resultado da busca
          console.log("Database search result:", {
            userFound: !!user,
            hasPassword: !!user?.hashedPassword,
          });

          if (!user || !user.hashedPassword) {
            console.log("User not found or no password");
            return null;
          }

          // Log antes da comparação de senha
          console.log("Attempting password comparison");

          const isValidPassword = await compare(
            credentials.password,
            user.hashedPassword
          );

          // Log do resultado da comparação
          console.log("Password comparison result:", isValidPassword);

          if (!isValidPassword) {
            console.log("Invalid password");
            return null;
          }

          // Log dos dados que serão retornados
          const userData = {
            id: user.id,
            email: user.email,
            name: user.firstName,
            type: user.type,
          };

          console.log(
            "Authentication successful. Returning user data:",
            userData
          );

          return userData;
        } catch (error) {
          console.error("Error in authorize function:", error);
          return null;
        }
      },
    }),
  ],
  debug: true,
};

export async function login(data: LoginFormData): Promise<LoginResponse> {
  const response = await fetch("/api/auth/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Falha na autenticação");
  }

  return response.json();
}

export async function checkAdminAuth() {
  const session = await checkAuth();

  if (session.user.type !== "admin") {
    throw new Error("Não autorizado - Acesso apenas para administradores");
  }

  return session;
}

export async function checkAuth(): Promise<CustomSession> {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    throw new Error("Não autenticado");
  }

  // Garantir que o tipo está correto
  const customSession: CustomSession = {
    ...session,
    user: {
      ...(session.user as any),
      id: (session.user as any).id,
      type: (session.user as any).type,
    },
  };

  return customSession;
}

export async function checkUserType(
  allowedTypes: string[]
): Promise<CustomSession> {
  const session = await checkAuth();

  if (!allowedTypes.includes(session.user.type)) {
    throw new Error(
      `Não autorizado - Acesso apenas para: ${allowedTypes.join(", ")}`
    );
  }

  return session;
}
