import * as z from "zod";

const baseSchema = z.object({
  firstName: z.string().min(2, "Nome deve ter no mínimo 2 caracteres"),
  lastName: z.string().min(2, "Sobrenome deve ter no mínimo 2 caracteres"),
  email: z.string().email("Email inválido"),
  whatsapp: z
    .string()
    .length(11, "WhatsApp deve ter 11 dígitos")
    .regex(/^\d+$/, "WhatsApp deve conter apenas números"),
  zipCode: z
    .string()
    .length(8, "CEP deve ter 8 dígitos")
    .regex(/^\d+$/, "CEP deve conter apenas números"),
  state: z.string().length(2, "Estado deve ter 2 caracteres"),
  city: z.string().min(2, "Cidade deve ter no mínimo 2 caracteres"),
  password: z.string().min(6, "Senha deve ter no mínimo 6 caracteres"),
  confirmPassword: z.string(),
  // Adicione os campos de geolocalização no schema base
  latitude: z.number().nullable().optional(),
  longitude: z.number().nullable().optional(),
  addressPrecision: z.string().nullable().optional(),
});

const patientSchema = baseSchema.extend({
  type: z.literal("patient"),
});

const dentistSchema = baseSchema.extend({
  type: z.literal("dentist"),
  cpf: z.string().min(11, "CPF inválido"),
  cro: z.string().min(4, "CRO inválido"),
  croState: z.string().length(2, "Estado do CRO deve ter 2 caracteres"),
  address: z.string().min(3, "Endereço deve ter no mínimo 3 caracteres"),
  number: z.string().min(1, "Número é obrigatório"),
  complement: z.string().optional(),
  district: z.string().min(2, "Bairro deve ter no mínimo 2 caracteres"),
});

export const registerSchema = z
  .discriminatedUnion("type", [patientSchema, dentistSchema])
  .refine((data) => data.password === data.confirmPassword, {
    message: "As senhas não coincidem",
    path: ["confirmPassword"],
  });

export const loginSchema = z.object({
  email: z.string().email("Email inválido"),
  password: z.string().min(6, "Senha deve ter no mínimo 6 caracteres"),
});

export type RegisterFormData = z.infer<typeof registerSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;
