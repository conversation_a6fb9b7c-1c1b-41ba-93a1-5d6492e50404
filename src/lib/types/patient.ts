import { z } from "zod";

export const patientSchema = z.object({
  firstName: z.string().min(2, "Nome deve ter no mínimo 2 caracteres"),
  lastName: z.string().min(2, "Sobrenome deve ter no mínimo 2 caracteres"),
  email: z.string().email("Email inválido"),
  whatsapp: z.string().min(11, "WhatsApp inválido"),
  zipCode: z.string().length(8, "CEP deve ter 8 dígitos"),
  state: z.string().length(2, "Estado deve ter 2 caracteres"),
  city: z.string().min(2, "Cidade deve ter no mínimo 2 caracteres"),
});

export type PatientFormData = z.infer<typeof patientSchema>;