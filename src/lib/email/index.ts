import nodemailer from "nodemailer";

interface EmailConfig {
  host: string;
  port: number;
  user: string;
  pass: string;
  subject: string;
}

const emailConfig: EmailConfig = {
  host: process.env.SMTP_HOST || "",
  port: Number(process.env.SMTP_PORT) || 587,
  user: process.env.SMTP_USER || "",
  pass: process.env.SMTP_PASS || "",
  subject: process.env.SMTP_SUBJECT || "Sistema Dental",
};

interface SendEmailParams {
  to: string;
  subject: string;
  html: string;
}

export async function sendEmail({ to, subject, html }: SendEmailParams) {
  if (!emailConfig.host || !emailConfig.user || !emailConfig.pass) {
    throw new Error("Configurações SMTP incompletas");
  }

  const transporter = nodemailer.createTransport({
    host: emailConfig.host,
    port: emailConfig.port,
    secure: false, // true para 465, false para outras portas
    auth: {
      user: emailConfig.user,
      pass: emailConfig.pass,
    },
    tls: {
      rejectUnauthorized: false, // Permite certificados auto-assinados
    },
  });

  const info = await transporter.sendMail({
    from: `"${emailConfig.subject}" <${emailConfig.user}>`,
    to,
    subject,
    html,
  });

  return info;
}
