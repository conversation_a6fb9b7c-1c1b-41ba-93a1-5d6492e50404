import nodemailer from "nodemailer";

interface EmailConfig {
  host: string;
  port: number;
  user: string;
  pass: string;
}

const emailConfig: EmailConfig = {
  host: process.env.SMTP_HOST || "",
  port: Number(process.env.SMTP_PORT) || 587,
  user: process.env.SMTP_USER || "",
  pass: process.env.SMTP_PASS || "",
};

interface WelcomeEmailParams {
  to: string;
  firstName: string;
  userType: "dentist" | "patient";
}

interface EmailResult {
  success: boolean;
  error?: string;
  messageId?: string;
}

export async function sendWelcomeEmail({
  to,
  firstName,
  userType,
}: WelcomeEmailParams): Promise<EmailResult> {
  try {
    console.log("Iniciando envio de email com configurações:", {
      host: emailConfig.host,
      port: emailConfig.port,
      user: emailConfig.user,
      authConfigured: !!emailConfig.pass,
    });

    if (!emailConfig.host || !emailConfig.user || !emailConfig.pass) {
      throw new Error("Configurações SMTP incompletas");
    }

    const transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: false, // true para 465, false para outras portas
      auth: {
        user: emailConfig.user,
        pass: emailConfig.pass,
      },
      tls: {
        rejectUnauthorized: false, // Permite certificados auto-assinados
      },
    });

    // Verifica a conexão
    await transporter.verify();
    console.log("Conexão SMTP verificada com sucesso");

    const userTypeText = userType === "dentist" ? "Dentista" : "Paciente";

    const info = await transporter.sendMail({
      from: `"Sistema Dental" <${emailConfig.user}>`,
      to,
      subject: "Bem-vindo ao Sistema Dental",
      html: `
   <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 30px; border-radius: 8px; box-shadow: 0 4px 10px rgba(0,0,0,0.05);">
  <div style="background-color: #ffffff; padding: 20px 30px; border-radius: 6px;">
    <h2 style="color: #333333;">Olá, ${firstName}!</h2>
    <p style="font-size: 16px; color: #555555;">Seja bem-vindo ao nosso sistema como <strong>${userTypeText}</strong>.</p>
    <p style="font-size: 16px; color: #555555;">Estamos muito felizes em ter você conosco. Esperamos que sua experiência seja excelente!</p>
    <p style="font-size: 16px; color: #555555;">Atenciosamente,<br>
      <span style="color: #222222; font-weight: bold;">Equipe Céu na Boca</span>
    </p>
  </div>
  <p style="text-align: center; font-size: 12px; color: #aaaaaa; margin-top: 20px;">© 2025 Céu na Boca. Todos os direitos reservados.</p>
</div>
      `,
    });

    console.log("Email enviado com sucesso:", info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error("Erro ao enviar email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido",
    };
  }
}
