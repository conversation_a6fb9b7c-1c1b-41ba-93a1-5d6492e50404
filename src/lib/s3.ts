// lib/s3.ts
import { S3Client } from "@aws-sdk/client-s3";

export const s3 = new S3Client({
  region: "us-east-1", // pode ser qualquer string
  endpoint: process.env.NODE_ENV === "production" 
    ? `http://${process.env.MINIO_ENDPOINT}:${process.env.MINIO_API_PORT}`
    : "http://localhost:9000",
  credentials: {
    accessKeyId: process.env.MINIO_ROOT_USER || "minioadmin",
    secretAccessKey: process.env.MINIO_ROOT_PASSWORD || "minioadmin",
  },
  forcePathStyle: true, // necessário para MinIO
});
