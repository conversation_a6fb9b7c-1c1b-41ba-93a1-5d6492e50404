"use client";

import { useEffect, useState, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { TermosDialog } from "../components/TermosDialog";
import { ChevronLeft } from "lucide-react";
import { Loader } from "@/components/ui/loader";

interface ProfileData {
  email: string;
  whatsapp: string;
  address: string;
  zipCode: string;
  state: string;
  city: string;
  number: string;
  district: string;
  complement?: string;
}

const formatWhatsApp = (value: string) => {
  const numbers = value.replace(/\D/g, "");
  if (numbers.length <= 2) return numbers;
  if (numbers.length <= 7)
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
  return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
};

export default function MinhaEstrelaPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [publicId, setPublicId] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [termosDialogOpen, setTermosDialogOpen] = useState(false);

  const form = useForm<ProfileData>({
    defaultValues: {
      email: "",
      whatsapp: "",
      address: "",
    },
  });

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith("image/")) {
      toast.error("Por favor, selecione uma imagem válida");
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error("A imagem deve ter menos que 5MB");
      return;
    }

    setIsUploading(true);
    const formData = new FormData();
    formData.append("image", file);

    try {
      const response = await fetch("/api/user/upload-image", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Erro no upload da imagem");
      }

      const data = await response.json();
      setImageUrl(data.imageUrl);
      setPublicId(data.publicId);
      toast.success("Imagem enviada com sucesso!");
    } catch (error) {
      console.error("Erro ao fazer upload:", error);
      toast.error("Erro ao enviar imagem");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteImage = async () => {
    if (!publicId) return;

    try {
      const response = await fetch("/api/user/upload-image", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ publicId }),
      });

      if (!response.ok) {
        throw new Error("Erro ao deletar imagem");
      }

      setImageUrl(null);
      setPublicId(null);
      toast.success("Imagem removida com sucesso!");
    } catch (error) {
      console.error("Erro ao deletar imagem:", error);
      toast.error("Erro ao remover imagem");
    }
  };

  useEffect(() => {
    async function fetchProfile() {
      try {
        const response = await fetch("/api/user/profile");
        if (!response.ok) throw new Error("Falha ao carregar perfil");
        const data = await response.json();

        const fullAddress = `${data.address}, ${data.number}${
          data.complement ? `, ${data.complement}` : ""
        } - ${data.district}, ${data.city} - ${data.state}, ${data.zipCode}`;

        form.reset({
          ...data,
          address: fullAddress,
        });
        setImageUrl(data.image); // Adicionado para carregar a imagem existente
      } catch (error) {
        console.error("Erro ao carregar perfil:", error);
        setError("Erro ao carregar dados do perfil");
      } finally {
        setIsLoading(false);
      }
    }

    fetchProfile();
  }, [form]);

  if (isLoading) {
    return (
      <div className="relative min-h-screen bg-gradient-to-br from-[#EBF3FF] to-white">
        <Loader
          isLoading={true}
          className="bg-gradient-to-br from-[#EBF3FF]/80 to-white/80"
        />
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-[#EBF3FF] to-white p-4 flex flex-col items-center">
      <div className="flex items-center w-full max-w-2xl mb-8">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full"
          onClick={() => router.push("/dentista/home")}
        >
          <ChevronLeft className="h-5 w-5 text-blue-600" />
        </Button>
        <h1 className="text-xl font-semibold text-blue-600 flex-1 text-center">
          Minha Estrela
        </h1>
        <div className="w-10" /> {/* Spacer to balance the button */}
      </div>

      <Card className="max-w-2xl w-full">
        <CardContent>
          {/* Área de Upload de Imagem */}
          <div className="mb-6 flex justify-center">
            <div className="relative">
              <div
                className="group w-40 h-40 rounded-full border-2 border-dashed border-blue-300 flex flex-col items-center justify-center cursor-pointer hover:border-blue-500 transition-colors overflow-hidden bg-white"
                onClick={() => !isUploading && fileInputRef.current?.click()}
              >
                {imageUrl ? (
                  <div className="relative w-full h-full">
                    <Image
                      src={imageUrl}
                      alt="Imagem do perfil"
                      fill
                      priority
                      className="object-cover"
                      unoptimized
                    />
                    {/* Overlay escuro com texto no hover */}
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <p className="text-white text-sm text-center px-2">
                        Clique para alterar imagem
                      </p>
                    </div>
                  </div>
                ) : (
                  <>
                    <svg
                      className="w-12 h-12 text-blue-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    <p className="mt-2 text-sm text-blue-500 text-center px-2">
                      {isUploading
                        ? "Enviando..."
                        : "Clique para adicionar uma foto"}
                    </p>
                  </>
                )}
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
                disabled={isUploading}
              />
            </div>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} disabled />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="whatsapp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>WhatsApp</FormLabel>
                    <FormControl>
                      <Input 
                        {...field} 
                        disabled 
                        value={formatWhatsApp(field.value)}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Endereço</FormLabel>
                    <FormControl>
                      <Input {...field} disabled />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>

          {/* Botões de Edição - Responsivos */}
          <div className="flex flex-col md:flex-row gap-4 pt-6 border-t mt-8">
            <Button
              variant="outline"
              className="flex-1 h-11 text-[#898989] font-bold text-lg"
              onClick={() => router.push("/dentista/cadastro-completo")}
            >
              Editar Perfil
            </Button>
            <Button
              variant="outline"
              className="flex-1 h-11 text-[#898989] font-bold text-lg"
              onClick={() => router.push("/dentista/especialidades")}
            >
              Editar áreas de atuação
            </Button>
            <Button
              variant="outline"
              className="flex-1 h-11 text-[#898989] font-bold text-lg"
              onClick={() => router.push("/dentista/alteracao")}
            >
              Editar alterações
            </Button>
          </div>

          {/* Termos de Uso */}
          <div className="pt-6">
            <Button
              variant="ghost"
              className="w-full text-[#898989]"
              onClick={() => setTermosDialogOpen(true)}
            >
              Termos de Uso
            </Button>
          </div>
        </CardContent>
      </Card>

      <TermosDialog
        isOpen={termosDialogOpen}
        onClose={() => setTermosDialogOpen(false)}
      />
    </main>
  );
}
