"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  getDentistVisitCount,
  getDentistContactCount,
} from "@/app/actions/dentist-actions";
import { getUserProfile } from "@/app/actions/user-actions";
import { Loader } from "@/components/ui/loader";

// Função para formatar número com zeros à esquerda
const formatNumber = (num: number): string => {
  return num.toString().padStart(2, '0');
};

export default function MeuEspacoPage() {
  const router = useRouter();
  const [totalPacientes, setTotalPacientes] = useState<number>(0);
  const [totalVisitas, setTotalVisitas] = useState<number>(0);
  const [totalInteressados, setTotalInteressados] = useState<number>(0);
  const [estado, setEstado] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      try {
        // Buscar dados do usuário logado
        const userData = await getUserProfile();
        setEstado(userData.estado);

        // Buscar total de pacientes do mesmo estado
        const responsePacientes = await fetch("/api/dentista/total-pacientes");
        if (!responsePacientes.ok)
          throw new Error("Falha ao carregar dados de pacientes");
        const dataPacientes = await responsePacientes.json();
        setTotalPacientes(dataPacientes.total);

        // Buscar contagem de visitas usando a server action
        const visitCount = await getDentistVisitCount();
        setTotalVisitas(visitCount);

        // Buscar contagem de contatos usando a nova server action
        const contactCount = await getDentistContactCount();
        setTotalInteressados(contactCount);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="relative min-h-screen bg-gradient-to-br from-[#EBF3FF] to-white">
        <Loader
          isLoading={true}
          className="bg-gradient-to-br from-[#EBF3FF]/80 to-white/80"
        />
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-[#EBF3FF] to-white p-4 flex flex-col items-center">
      <div className="w-full max-w-2xl md:max-w-4xl lg:max-w-5xl">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={() => router.push("/dentista/home")}
          >
            <ChevronLeft className="h-5 w-5 text-blue-600" />
          </Button>
          <h1 className="text-xl font-semibold text-blue-600 flex-1 text-center">
            Meu Espaço
          </h1>
          <div className="w-10" /> {/* Spacer para equilibrar o botão */}
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Card 1 - Usuários Espaciais */}
              <div className="min-h-45 bg-blue-600 rounded-xl p-6 text-white flex flex-col items-center justify-between">
                  <p className="text-lg">Usuários Espaciais</p>
                  <p className="text-3xl font-bold">{formatNumber(totalPacientes)}</p>
                  <p className="text-sm opacity-80">Usuários Espaciais em {estado}</p>
              </div>

              {/* Card 2 - Visitantes do Perfil */}
              <div className="min-h-45 bg-blue-600 rounded-xl p-6 text-white flex flex-col items-center justify-between">
                  <p className="text-lg">Usuários cadentes</p>
                  <p className="text-3xl font-bold">{formatNumber(totalVisitas)}</p>
                  <p className="text-sm opacity-80">Usuários que visitaram seu perfil</p>
              </div>

              {/* Card 3 - Usuários Interessados */}
              <div className="min-h-45 bg-blue-600 rounded-xl p-6 text-white flex flex-col items-center justify-between">
                  <p className="text-lg">Usuários interessados</p>
                  <p className="text-3xl font-bold">{formatNumber(totalInteressados)}</p>
                  <p className="text-sm opacity-80">Usuários que tentaram entrar em contato</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
