import { getTemplate, updateTemplate } from "@/app/actions/queries";
import { TemplateForm } from "@/components/admin/template-form";
import { notFound } from "next/navigation";

export default async function EditTemplatePage(props: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await props.params;

  try {
    const template = await getTemplate(id);

    const handleSubmit = async (data: any) => {
      "use server";
      await updateTemplate(id, data);
    };

    return (
      <div className="container mx-auto py-10">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Editar Template</h1>
          <TemplateForm
            initialData={template}
            isEditing={true}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    );
  } catch (error) {
    notFound();
  }
}
