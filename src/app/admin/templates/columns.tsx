"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useState, useEffect } from "react";
import { Loader } from "@/components/ui/loader";

export type Template = {
  id: string;
  key: string;
  title: string;
  template: string;
  createdAt: string;
};

interface TemplatesTableProps {
  templates: Template[];
  totalTemplates: number;
  pageSize: number;
}

export function TemplatesTable({
  templates: initialTemplates,
  totalTemplates,
  pageSize,
}: TemplatesTableProps) {
  const router = useRouter();
  const [templates, setTemplates] = useState<Template[]>(initialTemplates);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const totalPages = Math.ceil(totalTemplates / pageSize);

  useEffect(() => {
    fetchTemplates(currentPage);
  }, [currentPage]);

  const fetchTemplates = async (page: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/admin/templates?page=${page}&pageSize=${pageSize}`
      );
      if (!response.ok) throw new Error("Erro ao carregar templates");
      const data = await response.json();
      setTemplates(data.items || []);
    } catch (error) {
      toast.error("Erro ao carregar templates");
      setTemplates([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (templateId: string) => {
    if (confirm("Tem certeza que deseja excluir este template?")) {
      try {
        const response = await fetch(`/api/admin/templates/${templateId}`, {
          method: "DELETE",
        });

        if (!response.ok) throw new Error("Erro ao excluir template");

        toast.success("Template excluído com sucesso");
        fetchTemplates(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir template");
      }
    }
  };

  if (isLoading) {
    return <Loader isLoading={isLoading}></Loader>;
  }

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Chave</TableHead>
              <TableHead>Título</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {templates.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center py-8">
                  Nenhum template encontrado.
                </TableCell>
              </TableRow>
            ) : (
              templates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>{template.key}</TableCell>
                  <TableCell>{template.title}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        asChild
                        className="cursor-pointer"
                      >
                        <Link href={`/admin/templates/${template.id}`}>
                          <Pencil className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="cursor-pointer"
                        onClick={() => handleDelete(template.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {templates.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                )
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages &&
                    setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
