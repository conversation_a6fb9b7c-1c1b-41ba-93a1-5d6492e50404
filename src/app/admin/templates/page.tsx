"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Trash, Plus } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Loader } from "@/components/ui/loader";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type Template = {
  id: string;
  key: string;
  title: string;
  template: string;
  createdAt: string;
};

type TemplateKey = "WELCOME_EMAIL" | "PASSWORD_RESET" | "APPOINTMENT_CONFIRMATION" | "APPOINTMENT_REMINDER" | "APPOINTMENT_CANCELLATION";

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function TemplatesPage() {
  const router = useRouter();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalTemplates, setTotalTemplates] = useState(0);
  const [keyFilter, setKeyFilter] = useState<TemplateKey | "all">("all");
  const [titleFilter, setTitleFilter] = useState("");
  const pageSize = 10;
  const totalPages = Math.ceil(totalTemplates / pageSize);

  const debouncedKeyFilter = useDebounce(keyFilter, 1000);
  const debouncedTitleFilter = useDebounce(titleFilter, 1000);

  useEffect(() => {
    setCurrentPage(1);
    fetchTemplates(1);
  }, [debouncedKeyFilter, debouncedTitleFilter]);

  useEffect(() => {
    fetchTemplates(currentPage);
  }, [currentPage]);

  const fetchTemplates = async (page: number) => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(debouncedKeyFilter && debouncedKeyFilter !== "all" && { key: debouncedKeyFilter }),
        ...(debouncedTitleFilter && { title: debouncedTitleFilter }),
      });

      const response = await fetch(`/api/admin/templates?${params.toString()}`);
      if (!response.ok) throw new Error("Erro ao carregar templates");
      const data = await response.json();
      setTemplates(data.items || []);
      setTotalTemplates(data.total || 0);
    } catch (error) {
      toast.error("Erro ao carregar templates");
      setTemplates([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (templateId: string) => {
    if (confirm("Tem certeza que deseja excluir este template?")) {
      try {
        const response = await fetch(`/api/admin/templates/${templateId}`, {
          method: "DELETE",
        });

        if (!response.ok) throw new Error("Erro ao excluir template");

        toast.success("Template excluído com sucesso");
        fetchTemplates(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir template");
      }
    }
  };

  const clearFilters = () => {
    setKeyFilter("all");
    setTitleFilter("");
  };

  if (isLoading) {
    return <Loader isLoading={isLoading}></Loader>;
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Templates de Email</h1>
        <Button asChild className="cursor-pointer">
          <Link href="/admin/templates/new">
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline ml-2">Novo Template</span>
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
        <div className="w-full">
          <label className="text-sm font-medium mb-2 block">Tipo de Template</label>
          <Select 
            value={keyFilter} 
            onValueChange={(value) => setKeyFilter(value as TemplateKey | "all")}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Filtrar por tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="WELCOME_EMAIL">Email de Boas-vindas</SelectItem>
              <SelectItem value="PASSWORD_RESET">Redefinição de Senha</SelectItem>
              <SelectItem value="APPOINTMENT_CONFIRMATION">Confirmação de Consulta</SelectItem>
              <SelectItem value="APPOINTMENT_REMINDER">Lembrete de Consulta</SelectItem>
              <SelectItem value="APPOINTMENT_CANCELLATION">Cancelamento de Consulta</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-full">
          <label className="text-sm font-medium mb-2 block">Título</label>
          <Input
            placeholder="Filtrar por título"
            value={titleFilter}
            onChange={(e) => setTitleFilter(e.target.value)}
          />
        </div>
      </div>

      <div className="flex justify-end mb-6">
        <Button variant="outline" onClick={clearFilters} className="w-full sm:w-auto">
          Limpar Filtros
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Chave</TableHead>
              <TableHead>Título</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {templates.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center py-8">
                  Nenhum template encontrado.
                </TableCell>
              </TableRow>
            ) : (
              templates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>{template.key}</TableCell>
                  <TableCell>{template.title}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        asChild
                        className="cursor-pointer"
                      >
                        <Link href={`/admin/templates/${template.id}`}>
                          <Pencil className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="cursor-pointer"
                        onClick={() => handleDelete(template.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {templates.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                )
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages &&
                    setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
