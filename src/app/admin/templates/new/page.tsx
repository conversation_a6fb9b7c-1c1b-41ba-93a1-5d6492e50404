import { createTemplate } from "@/app/actions/queries";
import { TemplateForm } from "@/components/admin/template-form";

export default function NewTemplatePage() {
  const handleSubmit = async (data: any) => {
    "use server";
    await createTemplate(data);
  };

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Novo Template</h1>
        <TemplateForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
}
