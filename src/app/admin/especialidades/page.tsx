"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useLoading } from "../layout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
} from "@/components/ui/pagination";
import {
  getSpecialties,
  deleteSpecialty,
} from "@/app/actions/specialty-actions";
import { Input } from "@/components/ui/input";

interface Specialty {
  id: string;
  name: string;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function SpecialtiesPage() {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [specialties, setSpecialties] = useState<Specialty[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nameFilter, setNameFilter] = useState("");
  const pageSize = 10;

  const debouncedNameFilter = useDebounce(nameFilter, 1000);

  useEffect(() => {
    fetchSpecialties(currentPage);
  }, [currentPage, debouncedNameFilter]);

  const fetchSpecialties = async (page: number) => {
    try {
      setIsLoading(true);
      const { specialties, total } = await getSpecialties(page, pageSize, debouncedNameFilter);
      setSpecialties(specialties);
      setTotalPages(Math.ceil(total / pageSize));
    } catch (error) {
      toast.error("Erro ao carregar lista de especialidades");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Tem certeza que deseja excluir esta especialidade?")) {
      try {
        setIsLoading(true);
        await deleteSpecialty(id);
        toast.success("Especialidade excluída com sucesso");
        fetchSpecialties(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir especialidade");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const clearFilters = () => {
    setNameFilter("");
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Especialidades</h1>
        <Button asChild>
          <Link href="/admin/especialidades/novo">
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline ml-2">Nova especialidade</span>
          </Link>
        </Button>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <label className="text-sm font-medium mb-2 block">Nome</label>
          <Input
            placeholder="Filtrar por nome"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
          />
        </div>
        <Button variant="outline" onClick={clearFilters} className="mt-7">
          Limpar Filtros
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {specialties.length === 0 ? (
              <TableRow>
                <TableCell colSpan={2} className="text-center py-8">
                  Nenhuma especialidade encontrada.
                </TableCell>
              </TableRow>
            ) : (
              specialties.map((specialty) => (
                <TableRow key={specialty.id}>
                  <TableCell>{specialty.name}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() =>
                          router.push(`/admin/especialidades/${specialty.id}`)
                        }
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() => handleDelete(specialty.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {specialties.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages && setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage >= totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage >= totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
