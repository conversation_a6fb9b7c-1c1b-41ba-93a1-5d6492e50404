import { getSpecialty, updateSpecialty } from "@/app/actions/specialty-actions";
import { SpecialtyForm } from "@/components/admin/specialty-form";
import { notFound } from "next/navigation";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Editar Especialidade",
};

export default async function EditSpecialtyPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const specialty = await getSpecialty(resolvedParams.id);

    if (!specialty) {
      notFound();
    }

    const handleSubmit = async (data: any) => {
      "use server";
      await updateSpecialty(resolvedParams.id, data);
    };

    return (
      <div className="container mx-auto py-10">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Editar Especialidade</h1>
          <SpecialtyForm
            initialData={specialty}
            isEditing={true}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    );
  } catch (error) {
    notFound();
  }
}
