"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader } from "@/components/ui/loader";
import {
  Users,
  UserCog,
  User,
  LogOut,
  Menu,
  X,
  Mail,
  FileText,
  ListChecks,
  Clock,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { signOut } from "next-auth/react";
import { useState, useEffect, createContext, useContext } from "react";

// Criar contexto para o loading
export const LoadingContext = createContext({
  isLoading: false,
  setIsLoading: (loading: boolean) => {},
});

// Hook personalizado para usar o loading
export const useLoading = () => useContext(LoadingContext);

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  onClose?: () => void;
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  return (
    <LoadingContext.Provider value={{ isLoading, setIsLoading }}>
      <div className="flex h-screen overflow-hidden">
        {/* Menu Button for Mobile */}
        {isMobile && !isMobileMenuOpen && (
          <Button
            variant="ghost"
            size="icon"
            className="fixed top-4 left-4 z-[60] md:hidden text-slate-600 hover:text-slate-800"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </Button>
        )}

        {/* Sidebar */}
        <div
          className={cn(
            "fixed md:fixed top-0 left-0 h-full z-[60]",
            isMobile
              ? isMobileMenuOpen
                ? "translate-x-0"
                : "-translate-x-full"
              : "translate-x-0",
            "transition-transform duration-200 ease-in-out"
          )}
        >
          <Sidebar
            className="w-64 bg-slate-700 text-white h-full"
            onClose={() => setIsMobileMenuOpen(false)}
          />
        </div>

        {/* Overlay for mobile */}
        {isMobile && isMobileMenuOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-[55]"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}

        {/* Main Content with Loader */}
        <div className="flex-1 ml-0 md:ml-64 relative overflow-auto">
          <Loader isLoading={isLoading} />
          <main className="p-4 md:p-8">{children}</main>
        </div>
      </div>
    </LoadingContext.Provider>
  );
}

function Sidebar({ className, onClose }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();

  const handleSignOut = async () => {
    await signOut({
      redirect: true,
      callbackUrl: "/login",
    });
  };

  const sidebarItems = [
    {
      label: "Gerenciamento",
      items: [
        {
          title: "Administradores",
          href: "/admin",
          icon: UserCog,
        },
        {
          title: "Dentistas",
          href: "/admin/dentistas",
          icon: Users,
        },
        {
          title: "Pacientes",
          href: "/admin/pacientes",
          icon: User,
        },
        {
          title: "Especialidades",
          href: "/admin/especialidades",
          icon: ListChecks,
        },
        {
          title: "Urgências",
          href: "/admin/urgencias",
          icon: Clock, // Ou outro ícone que faça sentido para urgências
        },
      ],
    },
    {
      label: "Envio de Email",
      items: [
        {
          title: "Campanhas",
          href: "/admin/campanhas",
          icon: Mail,
        },
        {
          title: "Templates",
          href: "/admin/templates",
          icon: FileText,
        },
      ],
    },
  ];

  const handleNavigation = (href: string) => {
    router.push(href);
    if (onClose) onClose();
  };

  return (
    <div className={cn("pb-12 flex flex-col h-full", className)}>
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden absolute right-2 top-2 text-white hover:bg-white/10"
        onClick={onClose}
      >
        <X className="h-5 w-5" />
      </Button>

      <div className="space-y-4 py-4 flex-1">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold text-white">
            Painel Admin
          </h2>
          <ScrollArea className="px-1">
            <div className="space-y-6">
              {sidebarItems.map((section, index) => (
                <div key={index} className="space-y-2">
                  <h3 className="px-4 text-sm font-medium text-white/50">
                    {section.label}
                  </h3>
                  <div className="space-y-1">
                    {section.items.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Button
                          key={item.href}
                          variant={
                            pathname === item.href ? "secondary" : "ghost"
                          }
                          className={cn(
                            "w-full justify-start",
                            pathname === item.href
                              ? "bg-white/10 text-white hover:bg-white/20"
                              : "text-white/80 hover:bg-white/10 hover:text-white"
                          )}
                          onClick={() => handleNavigation(item.href)}
                        >
                          <Icon className="mr-2 h-4 w-4" />
                          {item.title}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      <div className="px-3 py-2 border-t border-white/10">
        <Button
          variant="ghost"
          className="w-full justify-start text-white/80 hover:text-white hover:bg-white/10"
          onClick={handleSignOut}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Sair
        </Button>
      </div>
    </div>
  );
}
