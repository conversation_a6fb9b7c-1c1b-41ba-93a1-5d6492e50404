"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Pencil, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useLoading } from "./layout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { deleteAdmin } from "@/app/actions/admin-actions";
import { Input } from "@/components/ui/input";

interface Admin {
  id: string;
  firstName: string;
  email: string;
}

interface PaginatedResponse {
  items: Admin[];
  total: number;
  page: number;
  pageSize: number;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function AdminPage() {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nameFilter, setNameFilter] = useState("");
  const [emailFilter, setEmailFilter] = useState("");
  const pageSize = 10;

  const debouncedNameFilter = useDebounce(nameFilter, 1000);
  const debouncedEmailFilter = useDebounce(emailFilter, 1000);

  useEffect(() => {
    fetchAdmins(currentPage);
  }, [currentPage, debouncedNameFilter, debouncedEmailFilter]);

  const fetchAdmins = async (page: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/admin?page=${page}&pageSize=${pageSize}&name=${debouncedNameFilter}&email=${debouncedEmailFilter}`
      );
      if (!response.ok) throw new Error("Erro ao carregar administradores");
      const data: PaginatedResponse = await response.json();
      setAdmins(data.items);
      setTotalPages(Math.ceil(data.total / pageSize));
    } catch (error) {
      toast.error("Erro ao carregar lista de administradores");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Tem certeza que deseja excluir este administrador?")) {
      try {
        setIsLoading(true);
        await deleteAdmin(id);
        toast.success("Administrador excluído com sucesso");
        fetchAdmins(currentPage);
      } catch (error) {
        toast.error(
          error instanceof Error
            ? error.message
            : "Erro ao excluir administrador"
        );
      } finally {
        setIsLoading(false);
      }
    }
  };

  const clearFilters = () => {
    setNameFilter("");
    setEmailFilter("");
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Administradores</h1>
        <Button
          className="cursor-pointer"
          onClick={() => router.push("/admin/novo")}
        >
          <Plus className="h-4 w-4" />
          <span className="hidden md:inline ml-2">Novo Administrador</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-12 gap-4 mb-6">
        <div className="sm:col-span-5">
          <label className="text-sm font-medium mb-2 block">Nome</label>
          <Input
            placeholder="Filtrar por nome"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
          />
        </div>
        <div className="sm:col-span-5">
          <label className="text-sm font-medium mb-2 block">Email</label>
          <Input
            placeholder="Filtrar por email"
            value={emailFilter}
            onChange={(e) => setEmailFilter(e.target.value)}
          />
        </div>
        <div className="sm:col-span-2 flex items-end">
          <Button variant="outline" onClick={clearFilters} className="w-full">
            Limpar Filtros
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Email</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {admins.map((admin) => (
              <TableRow key={admin.id}>
                <TableCell>{admin.firstName}</TableCell>
                <TableCell>{admin.email}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="cursor-pointer"
                      onClick={() => router.push(`/admin/edit/${admin.id}`)}
                      disabled={admin.email === "<EMAIL>"}
                      title={
                        admin.email === "<EMAIL>"
                          ? "Não é possível editar o administrador principal"
                          : ""
                      }
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      className="cursor-pointer"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleDelete(admin.id)}
                      disabled={admin.email === "<EMAIL>"}
                      title={
                        admin.email === "<EMAIL>"
                          ? "Não é possível excluir o administrador principal"
                          : ""
                      }
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="mt-4 flex justify-center">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() =>
                  currentPage > 1 && setCurrentPage((prev) => prev - 1)
                }
                className={
                  currentPage === 1 ? "pointer-events-none opacity-50" : ""
                }
                aria-disabled={currentPage === 1}
              />
            </PaginationItem>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <PaginationItem key={page}>
                <PaginationLink
                  onClick={() => setCurrentPage(page)}
                  isActive={currentPage === page}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                onClick={() =>
                  currentPage < totalPages && setCurrentPage((prev) => prev + 1)
                }
                className={
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : ""
                }
                aria-disabled={currentPage === totalPages}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
}
