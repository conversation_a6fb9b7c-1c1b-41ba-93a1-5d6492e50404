"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Settings, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useLoading } from "@/app/admin/layout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";

interface Campaign {
  id: string;
  name: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  lastSentAt: string | null;
}

interface PaginatedResponse {
  items: Campaign[];
  total: number;
  page: number;
  pageSize: number;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function CampanhasPage() {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nameFilter, setNameFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [createdAtFilter, setCreatedAtFilter] = useState<Date>();
  const [lastSentAtFilter, setLastSentAtFilter] = useState<Date>();
  const pageSize = 10;

  const debouncedNameFilter = useDebounce(nameFilter, 1000);

  useEffect(() => {
    fetchCampaigns(currentPage);
  }, [currentPage, debouncedNameFilter, typeFilter, createdAtFilter, lastSentAtFilter]);

  const fetchCampaigns = async (page: number) => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(nameFilter && { name: nameFilter }),
        ...(typeFilter && typeFilter !== "all" && { type: typeFilter }),
        ...(createdAtFilter && {
          createdAt: createdAtFilter.toISOString(),
        }),
        ...(lastSentAtFilter && {
          lastSentAt: lastSentAtFilter.toISOString(),
        }),
      });

      const response = await fetch(`/api/admin/campaigns?${params.toString()}`);
      if (!response.ok) throw new Error("Erro ao carregar campanhas");
      const data: PaginatedResponse = await response.json();
      setCampaigns(data.items);
      setTotalPages(Math.ceil(data.total / pageSize));
    } catch (error) {
      toast.error("Erro ao carregar lista de campanhas");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Tem certeza que deseja excluir esta campanha?")) {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/admin/campaigns/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) throw new Error("Erro ao excluir campanha");

        toast.success("Campanha excluída com sucesso");
        fetchCampaigns(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir campanha");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleConfigure = (campaign: Campaign) => {
    const path =
      campaign.type === "PATIENT"
        ? `/admin/campanhas/${campaign.id}/campanha-pacientes`
        : `/admin/campanhas/${campaign.id}/campanha-profissionais`;
    router.push(path);
  };

  const clearFilters = () => {
    setNameFilter("");
    setTypeFilter("all");
    setCreatedAtFilter(undefined);
    setLastSentAtFilter(undefined);
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Campanhas</h1>
        <Button asChild>
          <Link href="/admin/campanhas/new">
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline ml-2">Nova Campanha</span>
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="w-full">
          <label className="text-sm font-medium mb-2 block">Nome</label>
          <Input
            placeholder="Filtrar por nome"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
          />
        </div>
        <div className="w-full">
          <label className="text-sm font-medium mb-2 block">Tipo</label>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Filtrar por tipo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos</SelectItem>
              <SelectItem value="PATIENT">Pacientes</SelectItem>
              <SelectItem value="PROFESSIONAL">Profissionais</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-full">
          <label className="text-sm font-medium mb-2 block">Data de Criação</label>
          <DatePicker
            date={createdAtFilter}
            onSelect={setCreatedAtFilter}
            placeholder="Filtrar por data de criação"
          />
        </div>
        <div className="w-full">
          <label className="text-sm font-medium mb-2 block">Último Envio</label>
          <DatePicker
            date={lastSentAtFilter}
            onSelect={setLastSentAtFilter}
            placeholder="Filtrar por último envio"
          />
        </div>
      </div>

      <div className="flex justify-end mb-6">
        <Button variant="outline" onClick={clearFilters} className="w-full sm:w-auto">
          Limpar Filtros
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Tipo</TableHead>
              <TableHead>Criado em</TableHead>
              <TableHead>Último envio</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {campaigns.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  Nenhuma campanha encontrada.
                </TableCell>
              </TableRow>
            ) : (
              campaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>{campaign.name}</TableCell>
                  <TableCell>
                    {campaign.type === "PATIENT" ? "Pacientes" : "Profissionais"}
                  </TableCell>
                  <TableCell>
                    {new Date(campaign.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {campaign.lastSentAt
                      ? new Date(campaign.lastSentAt).toLocaleDateString()
                      : "Nunca enviado"}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleConfigure(campaign)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleDelete(campaign.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {campaigns.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages && setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
