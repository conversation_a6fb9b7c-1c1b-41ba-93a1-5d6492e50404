"use client";

import { CampaignForm } from "@/components/admin/campaign-form";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Template {
  id: string;
  title: string;
}

export default function NewCampaignPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await fetch("/api/admin/templates");
        if (!response.ok) throw new Error("Erro ao carregar templates");
        const data = await response.json();
        // Extrair o array de items da resposta paginada
        setTemplates(Array.isArray(data.items) ? data.items : []);
      } catch (error) {
        console.error("Erro ao carregar templates:", error);
        toast.error("Erro ao carregar templates");
        setTemplates([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  if (loading) {
    return <div className="container mx-auto py-10">Carregando...</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Nova Campanha</h1>
      <CampaignForm templates={templates} />
    </div>
  );
}
