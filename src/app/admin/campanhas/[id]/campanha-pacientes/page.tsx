"use client";

import { use } from "react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { toast } from "sonner";
import { Filter, ArrowLeft, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  getCampaignFilters,
  updateCampaignFilters,
  filtrarPacientes,
} from "@/app/actions/server-actions";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  whatsapp: string;
  city: string;
  state: string;
  selected?: boolean;
}

interface Alteracao {
  id: number;
  nome: string;
  descricao: string;
}

interface PageProps {
  params: Promise<{ id: string }>;
}

export default function CampanhaPacientesPage({ params }: PageProps) {
  const resolvedParams = use(params);
  const [loading, setLoading] = useState(true);
  const [selectedState, setSelectedState] = useState("");
  const [selectedAlteracoes, setSelectedAlteracoes] = useState<string[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [hasFiltered, setHasFiltered] = useState(false);
  const [alteracoes, setAlteracoes] = useState<Alteracao[]>([]);

  const estadosBrasil = [
    { sigla: "AC", nome: "Acre" },
    { sigla: "AL", nome: "Alagoas" },
    { sigla: "AP", nome: "Amapá" },
    { sigla: "AM", nome: "Amazonas" },
    { sigla: "BA", nome: "Bahia" },
    { sigla: "CE", nome: "Ceará" },
    { sigla: "DF", nome: "Distrito Federal" },
    { sigla: "ES", nome: "Espírito Santo" },
    { sigla: "GO", nome: "Goiás" },
    { sigla: "MA", nome: "Maranhão" },
    { sigla: "MT", nome: "Mato Grosso" },
    { sigla: "MS", nome: "Mato Grosso do Sul" },
    { sigla: "MG", nome: "Minas Gerais" },
    { sigla: "PA", nome: "Pará" },
    { sigla: "PB", nome: "Paraíba" },
    { sigla: "PR", nome: "Paraná" },
    { sigla: "PE", nome: "Pernambuco" },
    { sigla: "PI", nome: "Piauí" },
    { sigla: "RJ", nome: "Rio de Janeiro" },
    { sigla: "RN", nome: "Rio Grande do Norte" },
    { sigla: "RS", nome: "Rio Grande do Sul" },
    { sigla: "RO", nome: "Rondônia" },
    { sigla: "RR", nome: "Roraima" },
    { sigla: "SC", nome: "Santa Catarina" },
    { sigla: "SP", nome: "São Paulo" },
    { sigla: "SE", nome: "Sergipe" },
    { sigla: "TO", nome: "Tocantins" },
  ];

  useEffect(() => {
    const loadInitialData = async () => {
      await loadAlteracoes();
      await loadCampaignFilters();
    };

    loadInitialData();
  }, []);

  const loadAlteracoes = async () => {
    try {
      const response = await fetch("/assets/mocks/alteracoes.json");
      if (!response.ok) throw new Error("Erro ao carregar alterações");
      const data = await response.json();
      setAlteracoes(data);
    } catch (error) {
      toast.error("Erro ao carregar alterações");
    }
  };

  const loadCampaignFilters = async () => {
    try {
      if (!resolvedParams.id) {
        throw new Error("ID da campanha não encontrado");
      }

      const filters = await getCampaignFilters(resolvedParams.id);

      if (!filters) {
        throw new Error("Filtros não encontrados");
      }

      const state = filters.filterState ?? "";
      const alteracoesIds = filters.filterAlteracoes?.map(String) ?? [];

      setSelectedState(state);
      setSelectedAlteracoes(alteracoesIds);

      if (state || alteracoesIds.length > 0) {
        setHasFiltered(true);
        await handleFilter(state, alteracoesIds);
      } else {
        const allPatients = await filtrarPacientes({});
        setPatients(allPatients);
      }
    } catch (error) {
      console.error("Erro ao carregar filtros:", error);
      toast.error("Erro ao carregar filtros salvos");
      const allPatients = await filtrarPacientes({});
      setPatients(allPatients);
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = async (
    stateFilter?: string | null,
    alteracoesFilter?: string[]
  ) => {
    try {
      setLoading(true);

      const state = stateFilter || selectedState;
      const alteracoes = alteracoesFilter || selectedAlteracoes;

      if (!stateFilter && !alteracoesFilter) {
        await updateCampaignFilters(
          resolvedParams.id,
          state,
          alteracoes.map(Number)
        );
      }

      const filteredPatients = await filtrarPacientes({
        state: state === "todos" ? undefined : state,
        alteracaoIds: alteracoes.map(Number),
      });

      setPatients(filteredPatients);
      setHasFiltered(true);

      if (!stateFilter && !alteracoesFilter) {
        toast.success("Filtros aplicados com sucesso");
      }
    } catch (error) {
      toast.error("Erro ao aplicar filtros");
    } finally {
      setLoading(false);
    }
  };

  const handleSendEmails = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/campaigns/${resolvedParams.id}/send-emails`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Erro ao enviar emails");
      }

      const data = await response.json();
      toast.success(
        `Emails enviados: ${data.successful} com sucesso, ${data.failed} falhas de um total de ${data.total} pacientes`
      );
    } catch (error) {
      console.error("Erro ao enviar emails:", error);
      toast.error(
        error instanceof Error ? error.message : "Erro ao enviar emails"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-8">Carregando...</div>;
  }

  const alteracoesOptions = alteracoes.map((alteracao) => ({
    label: alteracao.nome,
    value: String(alteracao.id),
  }));

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Pacientes da Campanha</h1>
      </div>

      {/* Filters */}
      <div className="flex justify-between mb-6 items-end">
        <div className="flex space-x-4">
          <div>
            <label className="text-sm font-medium">Estado</label>
            <Select value={selectedState} onValueChange={setSelectedState}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Selecione um estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="todos">Todos os estados</SelectItem>
                  {estadosBrasil.map((estado) => (
                    <SelectItem key={estado.sigla} value={estado.sigla}>
                      {estado.nome}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div className="w-[500px]">
            {" "}
            {/* Aumentado de 300px para 500px */}
            <label className="text-sm font-medium">Alterações</label>
            <MultiSelect
              options={alteracoesOptions}
              placeholder="Selecione as alterações"
              onValueChange={setSelectedAlteracoes}
              defaultValue={selectedAlteracoes} // Passando as alterações salvas como defaultValue
              value={selectedAlteracoes} // Adicionando value para controle total
            />
          </div>
        </div>

        <Button onClick={() => handleFilter()}>
          <Filter className="mr-2 h-4 w-4" />
          Filtrar
        </Button>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>WhatsApp</TableHead>
              <TableHead>Cidade</TableHead>
              <TableHead>Estado</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {patients.map((patient) => (
              <TableRow key={patient.id}>
                <TableCell>{`${patient.firstName} ${patient.lastName}`}</TableCell>
                <TableCell>{patient.email}</TableCell>
                <TableCell>{patient.whatsapp}</TableCell>
                <TableCell>{patient.city}</TableCell>
                <TableCell>{patient.state}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Buttons */}
      <div className="mt-6 flex justify-between items-center">
        <Button variant="outline" asChild>
          <Link href="/admin/campanhas">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Link>
        </Button>

        <Button
          onClick={handleSendEmails}
          disabled={!hasFiltered}
          className="w-[200px]"
        >
          <Send className="mr-2 h-4 w-4" />
          Enviar Emails
        </Button>
      </div>
    </div>
  );
}
