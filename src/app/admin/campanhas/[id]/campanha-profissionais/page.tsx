"use client";

import { useState, useEffect, use } from "react";
import { Button } from "@/components/ui/button";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Filter, Send, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import { Loader } from "@/components/ui/loader";
import { getAllSpecialties } from "@/app/actions/specialty-actions";

interface Dentist {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  whatsapp: string;
  city: string;
  state: string;
  cro?: string;
  croState?: string;
  selected?: boolean;
}

interface Especialidade {
  id: string;
  nome: string;
}

export default function CampanhaProfissionaisPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const [dentists, setDentists] = useState<Dentist[]>([]);
  const [loading, setLoading] = useState(true);
  const [especialidades, setEspecialidades] = useState<Especialidade[]>([]);
  const [selectedEspecialidades, setSelectedEspecialidades] = useState<
    string[]
  >([]);
  const [hasFiltered, setHasFiltered] = useState(false);

  useEffect(() => {
    const loadInitialData = async () => {
      await loadEspecialidades();
      await loadCampaignFilters();
    };

    loadInitialData();
  }, []);

  const loadEspecialidades = async () => {
    try {
      const data = await getAllSpecialties();
      setEspecialidades(
        data.map((esp) => ({
          id: esp.id,
          nome: esp.name,
        }))
      );
    } catch (error) {
      console.error("Erro ao carregar especialidades:", error);
      toast.error("Erro ao carregar especialidades");
    }
  };

  const loadCampaignFilters = async () => {
    try {
      const response = await fetch(
        `/api/admin/campaigns/${resolvedParams.id}/dentist-filters`
      );
      if (!response.ok) throw new Error("Erro ao carregar filtros");

      const data = await response.json();
      const especialidadesIds = data.filterEspecialidades?.map(String) || [];

      setSelectedEspecialidades(especialidadesIds);

      if (especialidadesIds.length > 0) {
        setHasFiltered(true);
        await handleFilter(especialidadesIds);
      } else {
        await fetchDentists();
      }
    } catch (error) {
      console.error("Erro ao carregar filtros:", error);
      toast.error("Erro ao carregar filtros salvos");
      await fetchDentists();
    } finally {
      setLoading(false);
    }
  };

  const fetchDentists = async () => {
    try {
      const response = await fetch("/api/admin/dentists");
      if (!response.ok) throw new Error("Erro ao carregar dentistas");
      const data = await response.json();
      // Extrair o array de items da resposta paginada
      setDentists(Array.isArray(data.items) ? data.items : []);
      setLoading(false);
    } catch (error) {
      toast.error("Erro ao carregar lista de dentistas");
      setDentists([]); // Inicializar com array vazio em caso de erro
      setLoading(false);
    }
  };

  const saveCampaignFilters = async () => {
    try {
      const response = await fetch(
        `/api/admin/campaigns/${resolvedParams.id}/dentist-filters`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            especialidadeIds: selectedEspecialidades.map(String),
          }),
        }
      );

      if (!response.ok) throw new Error("Erro ao salvar filtros");
    } catch (error) {
      console.error("Erro ao salvar filtros:", error);
      toast.error("Erro ao salvar filtros");
    }
  };

  const handleFilter = async (especialidadesFilter?: string[]) => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      const especialidades = especialidadesFilter || selectedEspecialidades;

      if (especialidades.length > 0) {
        especialidades.forEach((especialidadeId) => {
          queryParams.append("especialidadeIds[]", especialidadeId);
        });
      }

      if (!especialidadesFilter) {
        await saveCampaignFilters();
      }

      const response = await fetch(
        `/api/admin/dentists/filter?${queryParams.toString()}`
      );
      if (!response.ok) throw new Error("Erro ao filtrar dentistas");

      const data = await response.json();
      setDentists(Array.isArray(data.items) ? data.items : []);
      setHasFiltered(true);

      if (!especialidadesFilter) {
        toast.success("Filtros aplicados com sucesso");
      }
    } catch (error) {
      toast.error("Erro ao aplicar filtros");
    } finally {
      setLoading(false);
    }
  };

  const handleSendEmails = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/campaigns/${resolvedParams.id}/send-dentist-emails`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Erro ao enviar emails");
      }

      const data = await response.json();
      toast.success(
        `Emails enviados: ${data.successful} com sucesso, ${data.failed} falhas de um total de ${data.total} profissionais`
      );
    } catch (error) {
      console.error("Erro ao enviar emails:", error);
      toast.error(
        error instanceof Error ? error.message : "Erro ao enviar emails"
      );
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="relative min-h-screen">
        <Loader isLoading={true} />
      </div>
    );
  }

  const especialidadesOptions = especialidades.map((especialidade) => ({
    label: especialidade.nome,
    value: String(especialidade.id),
  }));

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Profissionais da Campanha</h1>
      </div>

      {/* Filters */}
      <div className="flex justify-between mb-6 items-end">
        <div className="flex space-x-4">
          <div className="w-[500px]">
            <label className="text-sm font-medium">Especialidades</label>
            <MultiSelect
              options={especialidadesOptions}
              placeholder="Selecione as especialidades"
              onValueChange={setSelectedEspecialidades}
              defaultValue={selectedEspecialidades}
              value={selectedEspecialidades}
            />
          </div>
        </div>

        <Button
          onClick={() => handleFilter()}
          disabled={selectedEspecialidades.length === 0}
        >
          <Filter className="mr-2 h-4 w-4" />
          Filtrar
        </Button>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>WhatsApp</TableHead>
              <TableHead>Cidade</TableHead>
              <TableHead>Estado</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {dentists.map((dentist) => (
              <TableRow key={dentist.id}>
                <TableCell>{`${dentist.firstName} ${dentist.lastName}`}</TableCell>
                <TableCell>{dentist.email}</TableCell>
                <TableCell>{dentist.whatsapp}</TableCell>
                <TableCell>{dentist.city}</TableCell>
                <TableCell>{dentist.state}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Buttons */}
      <div className="mt-6 flex justify-between items-center">
        <Button variant="outline" asChild>
          <Link href="/admin/campanhas">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Link>
        </Button>

        <Button
          onClick={handleSendEmails}
          disabled={!hasFiltered}
          className="w-[200px]"
        >
          <Send className="mr-2 h-4 w-4" />
          Enviar Emails
        </Button>
      </div>
    </div>
  );
}
