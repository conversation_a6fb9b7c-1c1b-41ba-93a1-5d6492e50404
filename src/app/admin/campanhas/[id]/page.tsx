import { getCampaignAndTemplates } from "@/app/actions/queries";
import { CampaignForm } from "@/components/admin/campaign-form";
import { notFound } from "next/navigation";

export default async function EditCampaignPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  if (!id) {
    notFound();
  }

  try {
    const { campaign, templates } = await getCampaignAndTemplates(id);

    return (
      <div className="container mx-auto py-10">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Editar Campanha</h1>
          <CampaignForm
            initialData={campaign}
            isEditing={true}
            campaignId={id}
            templates={templates}
          />
        </div>
      </div>
    );
  } catch {
    notFound();
  }
}
