import { getUrgency, updateUrgency } from "@/app/actions/urgency-actions";
import { UrgencyForm } from "@/components/admin/urgency-form";
import { notFound } from "next/navigation";
import { Metadata } from "next";

export default async function EditUrgencyPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const resolvedParams = await params;
    const urgency = await getUrgency(resolvedParams.id);

    if (!urgency) {
      notFound();
    }

    const handleSubmit = async (data: any) => {
      "use server";
      await updateUrgency(resolvedParams.id, data);
    };

    return (
      <div className="container mx-auto py-10">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Editar Urgência</h1>
          <UrgencyForm
            initialData={urgency}
            isEditing={true}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    );
  } catch (error) {
    notFound();
  }
}
