"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Plus, Pencil, Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useLoading } from "../layout";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { getUrgencies, deleteUrgency } from "@/app/actions/urgency-actions";
import { useDebounce } from "@/lib/hooks/use-debounce";

interface Urgency {
  id: string;
  name: string;
}

export default function UrgenciesPage() {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [urgencies, setUrgencies] = useState<Urgency[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nameFilter, setNameFilter] = useState("");
  const debouncedNameFilter = useDebounce(nameFilter, 500);
  const pageSize = 10;

  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedNameFilter]);

  useEffect(() => {
    fetchUrgencies(currentPage);
  }, [currentPage, debouncedNameFilter]);

  const fetchUrgencies = async (page: number) => {
    try {
      setIsLoading(true);
      const { urgencies, total } = await getUrgencies(page, pageSize, debouncedNameFilter);
      setUrgencies(urgencies);
      setTotalPages(Math.ceil(total / pageSize));
    } catch (error) {
      toast.error("Erro ao carregar lista de urgências");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Tem certeza que deseja excluir esta urgência?")) {
      try {
        setIsLoading(true);
        await deleteUrgency(id);
        toast.success("Urgência excluída com sucesso");
        fetchUrgencies(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir urgência");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const clearFilters = () => {
    setNameFilter("");
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Urgências</h1>
        <Button asChild>
          <Link href="/admin/urgencias/novo">
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline ml-2">Nova urgência</span>
          </Link>
        </Button>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <label className="text-sm font-medium mb-2 block">Nome</label>
          <Input
            placeholder="Filtrar por nome"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
          />
        </div>
        <Button variant="outline" onClick={clearFilters} className="mt-7">
          Limpar Filtros
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {urgencies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={2} className="text-center py-8">
                  Nenhuma urgência encontrada.
                </TableCell>
              </TableRow>
            ) : (
              urgencies.map((urgency) => (
                <TableRow key={urgency.id}>
                  <TableCell>{urgency.name}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() =>
                          router.push(`/admin/urgencias/${urgency.id}`)
                        }
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() => handleDelete(urgency.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {urgencies.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages && setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage >= totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage >= totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
