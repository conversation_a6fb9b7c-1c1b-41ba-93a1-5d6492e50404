"use client";

import { AdminForm } from "@/components/admin/admin-form";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { toast } from "sonner";
import { Loader } from "@/components/ui/loader";

interface Admin {
  id: string;
  firstName: string;
  email: string;
}

export default function EditAdminPage() {
  const params = useParams();
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAdmin = async () => {
      try {
        const response = await fetch(`/api/admin/${params.id}`);
        if (!response.ok) throw new Error("Administrador não encontrado");
        const data = await response.json();
        setAdmin(data);
      } catch (error) {
        toast.error("Erro ao carregar administrador");
      } finally {
        setLoading(false);
      }
    };

    fetchAdmin();
  }, [params.id]);

  if (loading) {
    return (
      <div className="relative min-h-screen">
        <Loader isLoading={true} />
      </div>
    );
  }

  if (!admin) {
    return <div>Administrador não encontrado</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Editar Administrador</h1>
        <AdminForm
          initialData={{
            firstName: admin.firstName,
            email: admin.email,
          }}
          isEditing
          adminId={admin.id}
        />
      </div>
    </div>
  );
}
