import { getPaciente } from "@/app/actions/queries";
import { PatientForm } from "@/components/admin/patient-form";
import { notFound } from "next/navigation";

export default async function EditPacientePage(props: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await props.params;

  try {
    const paciente = await getPaciente(id);

    return (
      <div className="container mx-auto py-10">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-2xl font-bold mb-6">Editar Paciente</h1>
          <PatientForm
            initialData={paciente}
            isEditing={true}
            patientId={id}
          />
        </div>
      </div>
    );
  } catch (error) {
    notFound();
  }
}
