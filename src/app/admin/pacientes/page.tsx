"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Pencil, Trash } from "lucide-react";
import { toast } from "sonner";
import { PatientImport } from "@/components/admin/patient-import";
import { useLoading } from "../layout";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  whatsapp: string;
}

interface PaginatedResponse {
  items: Patient[];
  total: number;
  page: number;
  pageSize: number;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function PatientsPage() {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nameFilter, setNameFilter] = useState("");
  const [emailFilter, setEmailFilter] = useState("");
  const [whatsappFilter, setWhatsappFilter] = useState("");
  const pageSize = 10;

  const debouncedNameFilter = useDebounce(nameFilter, 1000);
  const debouncedEmailFilter = useDebounce(emailFilter, 1000);
  const debouncedWhatsappFilter = useDebounce(whatsappFilter, 1000);

  useEffect(() => {
    fetchPatients(currentPage);
  }, [currentPage, debouncedNameFilter, debouncedEmailFilter, debouncedWhatsappFilter]);

  const fetchPatients = async (page: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/admin/patients?page=${page}&pageSize=${pageSize}&name=${debouncedNameFilter}&email=${debouncedEmailFilter}&whatsapp=${debouncedWhatsappFilter}`
      );
      if (!response.ok) throw new Error("Erro ao carregar pacientes");
      const data: PaginatedResponse = await response.json();
      setPatients(data.items);
      setTotalPages(Math.ceil(data.total / pageSize));
    } catch (error) {
      toast.error("Erro ao carregar pacientes");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Tem certeza que deseja excluir este paciente?")) {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/admin/patients/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) throw new Error("Erro ao excluir paciente");

        toast.success("Paciente excluído com sucesso");
        fetchPatients(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir paciente");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const clearFilters = () => {
    setNameFilter("");
    setEmailFilter("");
    setWhatsappFilter("");
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Pacientes</h1>
        <div className="flex gap-4">
          <PatientImport onSuccess={() => fetchPatients(1)} />
          <Button
            className="cursor-pointer"
            onClick={() => router.push("/admin/pacientes/novo")}
          >
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline ml-2">Novo paciente</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <div>
          <label className="text-sm font-medium mb-2 block">Nome</label>
          <Input
            placeholder="Filtrar por nome"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">Email</label>
          <Input
            placeholder="Filtrar por email"
            value={emailFilter}
            onChange={(e) => setEmailFilter(e.target.value)}
          />
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">WhatsApp</label>
          <Input
            placeholder="Filtrar por WhatsApp"
            value={whatsappFilter}
            onChange={(e) => setWhatsappFilter(e.target.value)}
          />
        </div>
      </div>

      <div className="flex justify-end mb-6">
        <Button variant="outline" onClick={clearFilters}>
          Limpar Filtros
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>WhatsApp</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {patients.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  Nenhum paciente encontrado.
                </TableCell>
              </TableRow>
            ) : (
              patients.map((patient) => (
                <TableRow key={patient.id}>
                  <TableCell>{`${patient.firstName} ${patient.lastName}`}</TableCell>
                  <TableCell>{patient.email}</TableCell>
                  <TableCell>{patient.whatsapp}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() =>
                          router.push(`/admin/pacientes/edit/${patient.id}`)
                        }
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() => handleDelete(patient.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {patients.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages && setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
