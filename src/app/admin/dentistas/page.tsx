"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DentistImport } from "@/components/admin/dentist-import";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Pencil, Trash2, Plus, Info } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useLoading } from "../layout";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Dentist {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  cro: string;
  croState: string;
}

interface PaginatedResponse {
  items: Dentist[];
  total: number;
  page: number;
  pageSize: number;
}

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default function DentistsPage() {
  const router = useRouter();
  const { setIsLoading } = useLoading();
  const [dentists, setDentists] = useState<Dentist[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [nameFilter, setNameFilter] = useState("");
  const [emailFilter, setEmailFilter] = useState("");
  const [croFilter, setCroFilter] = useState("");
  const [croStateFilter, setCroStateFilter] = useState("");
  const pageSize = 10;

  const debouncedNameFilter = useDebounce(nameFilter, 1000);
  const debouncedEmailFilter = useDebounce(emailFilter, 1000);
  const debouncedCroFilter = useDebounce(croFilter, 1000);
  const debouncedCroStateFilter = useDebounce(croStateFilter, 1000);

  useEffect(() => {
    fetchDentists(currentPage);
  }, [currentPage, debouncedNameFilter, debouncedEmailFilter, debouncedCroFilter, debouncedCroStateFilter]);

  const fetchDentists = async (page: number) => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/admin/dentists?page=${page}&pageSize=${pageSize}&name=${debouncedNameFilter}&email=${debouncedEmailFilter}&cro=${debouncedCroFilter}&croState=${debouncedCroStateFilter}`
      );
      if (!response.ok) throw new Error("Erro ao carregar dentistas");
      const data: PaginatedResponse = await response.json();
      setDentists(data.items);
      setTotalPages(Math.ceil(data.total / pageSize));
    } catch (error) {
      toast.error("Erro ao carregar lista de dentistas");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm("Tem certeza que deseja excluir este dentista?")) {
      try {
        const response = await fetch(`/api/admin/dentists/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) throw new Error("Erro ao excluir dentista");

        toast.success("Dentista excluído com sucesso");
        fetchDentists(currentPage);
      } catch (error) {
        toast.error("Erro ao excluir dentista");
      }
    }
  };

  const clearFilters = () => {
    setNameFilter("");
    setEmailFilter("");
    setCroFilter("");
    setCroStateFilter("");
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dentistas</h1>
        <div className="flex gap-4 items-center">
          <div className="flex items-center gap-2">
            <DentistImport onSuccess={() => fetchDentists(1)} />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="w-80 p-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold">Modelo do arquivo XLSX</h4>
                    <p className="text-sm">O arquivo deve conter as seguintes colunas:</p>
                    <ul className="text-sm list-disc pl-4 space-y-1">
                      <li>Nome (obrigatório)</li>
                      <li>Sobrenome (obrigatório)</li>
                      <li>Email (obrigatório)</li>
                      <li>CRO (obrigatório)</li>
                      <li>Estado CRO (obrigatório)</li>
                    </ul>
                    <p className="text-sm text-muted-foreground mt-2">
                      Todas as colunas são obrigatórias e devem estar na primeira linha do arquivo.
                    </p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Button
            onClick={() => router.push("/admin/dentistas/novo")}
            className="cursor-pointer"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden md:inline ml-2">Novo dentista</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-12 gap-4 mb-6">
        <div className="sm:col-span-3">
          <label className="text-sm font-medium mb-2 block">Nome</label>
          <Input
            placeholder="Filtrar por nome"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
          />
        </div>
        <div className="sm:col-span-3">
          <label className="text-sm font-medium mb-2 block">Email</label>
          <Input
            placeholder="Filtrar por email"
            value={emailFilter}
            onChange={(e) => setEmailFilter(e.target.value)}
          />
        </div>
        <div className="sm:col-span-3">
          <label className="text-sm font-medium mb-2 block">CRO</label>
          <Input
            placeholder="Filtrar por CRO"
            value={croFilter}
            onChange={(e) => setCroFilter(e.target.value)}
          />
        </div>
        <div className="sm:col-span-3">
          <label className="text-sm font-medium mb-2 block">Estado do CRO</label>
          <Input
            placeholder="Filtrar por Estado CRO"
            value={croStateFilter}
            onChange={(e) => setCroStateFilter(e.target.value)}
          />
        </div>
      </div>

      <div className="flex justify-end mb-6">
        <Button variant="outline" onClick={clearFilters} className="w-full sm:w-auto">
          Limpar Filtros
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nome</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>CRO</TableHead>
              <TableHead>Estado CRO</TableHead>
              <TableHead className="w-[100px]">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {dentists.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  Nenhum dentista encontrado.
                </TableCell>
              </TableRow>
            ) : (
              dentists.map((dentist) => (
                <TableRow key={dentist.id}>
                  <TableCell>{`${dentist.firstName} ${dentist.lastName}`}</TableCell>
                  <TableCell>{dentist.email}</TableCell>
                  <TableCell>{dentist.cro}</TableCell>
                  <TableCell>{dentist.croState}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() =>
                          router.push(`/admin/dentistas/edit/${dentist.id}`)
                        }
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="cursor-pointer"
                        onClick={() => handleDelete(dentist.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {dentists.length > 0 && (
        <div className="mt-4 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    currentPage > 1 && setCurrentPage((prev) => prev - 1)
                  }
                  className={
                    currentPage === 1 ? "pointer-events-none opacity-50" : ""
                  }
                  aria-disabled={currentPage === 1}
                />
              </PaginationItem>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <PaginationItem key={page}>
                  <PaginationLink
                    onClick={() => setCurrentPage(page)}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    currentPage < totalPages && setCurrentPage((prev) => prev + 1)
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : ""
                  }
                  aria-disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
