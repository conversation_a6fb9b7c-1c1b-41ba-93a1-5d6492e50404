"use client";

import { DentistForm } from "@/components/admin/dentist-form";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { toast } from "sonner";
import { Loader } from "@/components/ui/loader";

interface Dentist {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  whatsapp: string;
  cpf: string;
  cro: string;
  croState: string;
  zipCode: string;
  state: string;
  city: string;
  address: string;
  number: string;
  complement: string;
  district: string;
}

export default function EditDentistPage() {
  const params = useParams();
  const [dentist, setDentist] = useState<Dentist | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDentist = async () => {
      try {
        const response = await fetch(`/api/admin/dentists/${params.id}`);
        if (!response.ok) throw new Error("Erro ao carregar dentista");
        const data = await response.json();
        // Garantir que nenhum campo seja null
        const sanitizedData = {
          ...data,
          firstName: data.firstName || "",
          lastName: data.lastName || "",
          email: data.email || "",
          whatsapp: data.whatsapp || "",
          cpf: data.cpf || "",
          cro: data.cro || "",
          croState: data.croState || "",
          zipCode: data.zipCode || "",
          state: data.state || "",
          city: data.city || "",
          address: data.address || "",
          number: data.number || "",
          complement: data.complement || "",
          district: data.district || "",
        };
        setDentist(sanitizedData);
      } catch (error) {
        toast.error("Erro ao carregar dados do dentista");
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchDentist();
    }
  }, [params.id]);

  if (loading) {
    return (
      <div className="relative min-h-screen">
        <Loader isLoading={true} />
      </div>
    );
  }

  if (!dentist) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Editar Dentista</h1>
        <DentistForm initialData={dentist} isEditing dentistId={dentist.id} />
      </div>
    </div>
  );
}
