import { db } from "@/lib/db";
import { checkAd<PERSON><PERSON><PERSON>, checkAuth, checkUserType } from "@/lib/auth";
import { hash } from "bcryptjs";
import { Template<PERSON>ey, CampaignType } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { PatientFormData } from "@/lib/types/patient";

// Tipos
interface DentistaUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  whatsapp?: string;
  cpf?: string;
  cro?: string;
  croState?: string;
  zipCode?: string;
  state?: string;
  city?: string;
  address?: string;
  number?: string;
  complement?: string;
  district?: string;
  password?: string;
}

// Tipo para os dados que serão enviados ao Prisma
interface PrismaUpdateData extends Omit<DentistaUpdateData, "password"> {
  hashedPassword?: string;
}

interface PacienteUpdateData {
  firstName?: string;
  lastName?: string;
  email?: string;
  whatsapp?: string;
  state?: string;
  city?: string;
}

interface CampaignData {
  name: string;
  type: CampaignType;
  emailTemplateId: string;
  filterState?: string | null;
  filterAlteracoes?: number[];
  filterEspecialidades?: string[];
}

// Profissionais
export async function getProfissionais(filters: {
  nome?: string;
  sobrenome?: string;
  especialidade?: string;
  cidade?: string;
  estado?: string;
  urgencia?: boolean;
  clinicaGeral?: boolean;
}) {
  const where: any = {
    type: "dentist",
    ...(filters.nome && {
      firstName: { contains: filters.nome, mode: "insensitive" },
    }),
    ...(filters.sobrenome && {
      lastName: { contains: filters.sobrenome, mode: "insensitive" },
    }),
    ...(filters.cidade && {
      city: { contains: filters.cidade, mode: "insensitive" },
    }),
    ...(filters.estado && { state: filters.estado.toUpperCase() }),
    ...(filters.urgencia && { urgency: { not: null } }),
    ...(filters.especialidade && {
      specialties: { some: { specialtyId: filters.especialidade } },
    }),
    ...(filters.clinicaGeral && {
      specialties: { some: { specialtyId: "CLINICO_GERAL" } },
    }),
  };

  return db.user.findMany({
    where,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      image: true,
      latitude: true,
      longitude: true,
      specialties: { select: { specialtyId: true } },
      urgency: true,
      city: true,
      state: true,
    },
  });
}

// Dentistas
export async function getDentistas(page: number, pageSize: number) {
  await checkAdminAuth();

  return Promise.all([
    db.user.count({ where: { type: "dentist" } }),
    db.user.findMany({
      where: { type: "dentist" },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        cro: true,
        croState: true,
        whatsapp: true,
        city: true,
        state: true,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: { firstName: "asc" },
    }),
  ]);
}

// Pacientes
export async function getPacientes(page: number, pageSize: number) {
  await checkAdminAuth();

  return Promise.all([
    db.user.count({ where: { type: "patient" } }),
    db.user.findMany({
      where: { type: "patient" },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        whatsapp: true,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: { firstName: "asc" },
    }),
  ]);
}

export async function getPaciente(id: string) {
  await checkAdminAuth();

  const patient = await db.user.findUnique({
    where: { id },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      whatsapp: true,
      zipCode: true,
      state: true,
      city: true,
      address: true,
      number: true,
      complement: true,
      district: true,
      UserChange: {
        select: {
          changeId: true,
        },
      },
    },
  });

  if (!patient) {
    throw new Error("Paciente não encontrado");
  }

  return patient;
}

export async function getPacienteForEdit(id: string) {
  await checkAdminAuth();

  const patient = await db.user.findUnique({
    where: { id },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      whatsapp: true,
      zipCode: true,
      state: true,
      city: true,
    },
  });

  if (!patient) {
    throw new Error("Paciente não encontrado");
  }

  return patient;
}

// Filtro de Pacientes
export async function filtrarPacientes(filters: {
  state?: string;
  alteracaoIds?: number[];
}) {
  await checkAdminAuth();

  const where: any = {
    type: "patient",
    ...(filters.state && filters.state !== "todos" && { state: filters.state }),
    ...(filters.alteracaoIds?.length && {
      UserChange: {
        some: {
          changeId: { in: filters.alteracaoIds },
        },
      },
    }),
  };

  return db.user.findMany({
    where,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      whatsapp: true,
      city: true,
      state: true,
    },
  });
}

// Templates
export async function getTemplates(page: number, pageSize: number) {
  await checkAdminAuth();

  const [templates, total] = await Promise.all([
    db.emailTemplate.findMany({
      select: {
        id: true,
        key: true,
        title: true,
        template: true,
        createdAt: true,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: { createdAt: "desc" },
    }),
    db.emailTemplate.count(),
  ]);

  return {
    templates,
    total,
  };
}

export async function getTemplate(id: string) {
  await checkAdminAuth();

  const template = await db.emailTemplate.findUnique({
    where: { id },
    select: {
      id: true,
      key: true,
      title: true,
      template: true,
    },
  });

  if (!template) {
    throw new Error("Template não encontrado");
  }

  return template;
}

export async function createTemplate(data: {
  key: TemplateKey;
  title: string;
  template: string;
}) {
  await checkAdminAuth();

  const template = await db.emailTemplate.create({
    data: {
      key: data.key,
      title: data.title,
      template: data.template,
    },
  });

  revalidatePath("/admin/templates");
  return template;
}

export async function updateTemplate(
  id: string,
  data: {
    key: TemplateKey;
    title: string;
    template: string;
  }
) {
  await checkAdminAuth();

  const template = await db.emailTemplate.update({
    where: { id },
    data: {
      key: data.key,
      title: data.title,
      template: data.template,
    },
  });

  revalidatePath("/admin/templates");
  return template;
}

export async function deleteTemplate(id: string) {
  await checkAdminAuth();

  await db.emailTemplate.delete({
    where: { id },
  });

  revalidatePath("/admin/templates");
}

// Especialidades do Dentista
export async function getDentistaEspecialidades(userId: string) {
  await checkUserType(["dentist", "admin"]);

  return Promise.all([
    db.user.findUnique({
      where: { id: userId },
      select: { urgency: true },
    }),
    db.userSpecialty.findMany({
      where: { userId },
      select: { specialtyId: true },
    }),
  ]);
}

// Campanhas
export async function getCampaigns(page: number, pageSize: number) {
  await checkAdminAuth();

  return Promise.all([
    db.campaign.findMany({
      take: pageSize,
      skip: (page - 1) * pageSize,
      orderBy: { createdAt: "desc" },
    }),
    db.campaign.count(),
  ]);
}

export async function createCampaign(data: CampaignData) {
  await checkAdminAuth();

  return db.campaign.create({
    data: {
      name: data.name,
      type: data.type,
      emailTemplateId: data.emailTemplateId,
      filterState: data.filterState || null,
      filterAlteracoes: data.filterAlteracoes || [],
      filterEspecialidades: data.filterEspecialidades || [],
    },
  });
}

export async function updateCampaign(id: string, data: Partial<CampaignData>) {
  await checkAdminAuth();

  return db.campaign.update({
    where: { id },
    data: {
      ...data,
      filterAlteracoes: data.filterAlteracoes || undefined,
      filterEspecialidades: data.filterEspecialidades || undefined,
    },
  });
}

export async function getCampaignFilters(id: string) {
  await checkAdminAuth();

  const campaign = await db.campaign.findUnique({
    where: { id },
    select: {
      filterState: true,
      filterAlteracoes: true,
    },
  });

  if (!campaign) {
    throw new Error("Campanha não encontrada");
  }

  return {
    filterState: campaign.filterState,
    filterAlteracoes: campaign.filterAlteracoes,
  };
}

export async function updateCampaignFilters(
  id: string,
  state: string,
  alteracaoIds: number[]
) {
  await checkAdminAuth();
  return db.campaign.update({
    where: { id },
    data: {
      filterState: state,
      filterAlteracoes: alteracaoIds,
    },
  });
}

export async function updateCampaignDentistFilters(
  id: string,
  especialidadeIds: string[]
) {
  await checkAdminAuth();
  return db.campaign.update({
    where: { id },
    data: {
      filterEspecialidades: especialidadeIds,
    },
    select: {
      id: true,
      filterEspecialidades: true,
    },
  });
}

export async function getCampaignAndTemplates(id: string) {
  await checkAdminAuth();

  const [campaign, templates] = await Promise.all([
    db.campaign.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        type: true,
        emailTemplateId: true,
        filterState: true,
        filterAlteracoes: true,
        filterEspecialidades: true,
      },
    }),
    db.emailTemplate.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: { title: "asc" },
    }),
  ]);

  if (!campaign) {
    throw new Error("Campanha não encontrada");
  }

  return {
    campaign,
    templates,
  };
}

// Dentistas
export async function updateDentista(id: string, data: DentistaUpdateData) {
  await checkAdminAuth();

  // Cria um objeto sem o campo password
  const { password, ...updateData } = data;

  // Se houver password, adiciona o hashedPassword
  if (password) {
    (updateData as PrismaUpdateData).hashedPassword = await hash(password, 12);
  }

  return db.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      whatsapp: true,
      cpf: true,
      cro: true,
      croState: true,
      zipCode: true,
      state: true,
      city: true,
      address: true,
      number: true,
      complement: true,
      district: true,
      type: true,
      updatedAt: true,
    },
  });
}

// Pacientes
export async function updatePaciente(
  id: string,
  data: PatientFormData
): Promise<void> {
  await checkAdminAuth();

  await db.user.update({
    where: { id },
    data: {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      whatsapp: data.whatsapp,
      zipCode: data.zipCode,
      state: data.state,
      city: data.city,
    },
  });
}

// User Changes
export async function getUserChanges() {
  const session = await checkAuth();
  // Agora o TypeScript sabe que session.user existe e tem um id
  return db.userChange.findMany({
    where: {
      userId: session.user.id,
    },
  });
}

export async function updateUserChanges(changes: number[]) {
  const session = await checkAuth();
  const userId = session.user.id;

  return db.$transaction(async (tx) => {
    await tx.userChange.deleteMany({
      where: { userId },
    });

    return Promise.all(
      changes.map((changeId) =>
        tx.userChange.create({
          data: {
            userId,
            changeId,
          },
        })
      )
    );
  });
}

// Professional Changes
export async function updateProfessionalChanges(changes: number[]) {
  const session = await checkAuth();
  const userId = session.user.id;

  return db.$transaction(async (tx) => {
    // Remove alterações antigas
    await tx.profissionalChange.deleteMany({
      where: { userId },
    });

    // Cria novas alterações
    return Promise.all(
      changes.map((changeId) =>
        tx.profissionalChange.create({
          data: {
            userId,
            changeId,
          },
        })
      )
    );
  });
}

// Especialidades
export async function updateDentistaEspecialidades(
  userId: string,
  especialidades: string[],
  urgencia: string | null
) {
  return db.$transaction(async (tx) => {
    // Atualiza a urgência
    await tx.user.update({
      where: { id: userId },
      data: {
        urgency: urgencia,
      },
    });

    // Sempre remove todas as especialidades existentes
    await tx.userSpecialty.deleteMany({
      where: { userId },
    });

    // Se houver especialidades selecionadas, cria as novas
    if (especialidades.length > 0) {
      await Promise.all(
        especialidades.map((specialtyId) =>
          tx.userSpecialty.create({
            data: {
              userId,
              specialtyId,
            },
          })
        )
      );
    }

    return true;
  });
}

export async function createPaciente(data: PatientFormData): Promise<void> {
  await checkAdminAuth();

  // Gerar uma senha padrão
  const password = "senha123"; // senha padrão
  const hashedPassword = await hash(password, 10);

  // Remover traços e outros caracteres do CEP
  const zipCode = data.zipCode.replace(/\D/g, "");

  await db.user.create({
    data: {
      ...data,
      zipCode,
      hashedPassword,
      type: "patient",
    },
  });
}
