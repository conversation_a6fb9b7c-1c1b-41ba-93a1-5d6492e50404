"use server";

import { db } from "@/lib/db";
import { hash } from "bcryptjs";
import { checkAdminAuth } from "@/lib/auth";
import { PatientFormData } from "@/lib/types/patient";
import { revalidatePath } from "next/cache";

export async function createPaciente(data: PatientFormData): Promise<void> {
  await checkAdminAuth();

  // Gerar uma senha padrão
  const password = "senha123"; // senha padrão
  const hashedPassword = await hash(password, 10);

  // Remover traços e outros caracteres do CEP
  const zipCode = data.zipCode.replace(/\D/g, "");

  await db.user.create({
    data: {
      ...data,
      zipCode,
      hashedPassword,
      type: "patient",
    },
  });

  revalidatePath("/admin/pacientes");
}

export async function updatePaciente(
  id: string,
  data: PatientFormData
): Promise<void> {
  await checkAd<PERSON><PERSON><PERSON>();

  await db.user.update({
    where: { id },
    data: {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      whatsapp: data.whatsapp,
      zipCode: data.zipCode,
      state: data.state,
      city: data.city,
    },
  });

  revalidatePath("/admin/pacientes");
}
