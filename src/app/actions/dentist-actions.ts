"use server";

import { db } from "@/lib/db";
import { checkAuth } from "@/lib/auth";

export async function registerDentistVisit(
  dentistId: string
): Promise<boolean> {
  try {
    const session = await checkAuth();
    const patientId = session.user.id;

    // Verifica se o usuário é um paciente
    const patient = await db.user.findUnique({
      where: { id: patientId },
      select: { type: true },
    });

    if (patient?.type !== "patient") {
      return false;
    }

    // Cria ou atualiza o registro de visita
    await db.dentistVisit.upsert({
      where: {
        dentistId_patientId: {
          dentistId,
          patientId,
        },
      },
      update: {
        visitedAt: new Date(),
      },
      create: {
        dentistId,
        patientId,
      },
    });

    return true;
  } catch (error) {
    console.error("Erro ao registrar visita:", error);
    return false;
  }
}

export async function getDentistVisitCount(): Promise<number> {
  try {
    const session = await checkAuth();
    const dentistId = session.user.id;

    // Verifica se o usuário é um dentista
    const dentist = await db.user.findUnique({
      where: { id: dentistId },
      select: { type: true },
    });

    if (dentist?.type !== "dentist") {
      return 0;
    }

    // Conta o número de pacientes únicos que visitaram o perfil
    const count = await db.dentistVisit.count({
      where: { dentistId },
    });

    return count;
  } catch (error) {
    console.error("Erro ao contar visitas:", error);
    return 0;
  }
}

export async function registerDentistContact(
  dentistId: string
): Promise<boolean> {
  try {
    const session = await checkAuth();
    const patientId = session.user.id;

    // Verifica se o usuário é um paciente
    const patient = await db.user.findUnique({
      where: { id: patientId },
      select: { type: true },
    });

    if (patient?.type !== "patient") {
      return false;
    }

    // Cria ou atualiza o registro de contato
    await db.dentistContact.upsert({
      where: {
        dentistId_patientId: {
          dentistId,
          patientId,
        },
      },
      update: {
        contactedAt: new Date(),
      },
      create: {
        dentistId,
        patientId,
      },
    });

    return true;
  } catch (error) {
    console.error("Erro ao registrar contato:", error);
    return false;
  }
}

export async function getDentistContactCount(): Promise<number> {
  try {
    const session = await checkAuth();
    const dentistId = session.user.id;

    // Verifica se o usuário é um dentista
    const dentist = await db.user.findUnique({
      where: { id: dentistId },
      select: { type: true },
    });

    if (dentist?.type !== "dentist") {
      return 0;
    }

    // Conta o número de pacientes únicos que entraram em contato
    const count = await db.dentistContact.count({
      where: { dentistId },
    });

    return count;
  } catch (error) {
    console.error("Erro ao contar contatos:", error);
    return 0;
  }
}
