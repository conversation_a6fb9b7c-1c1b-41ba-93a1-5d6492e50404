"use server";

import { db } from "@/lib/db";
import { checkAdminAuth } from "@/lib/auth";
import { revalidatePath } from "next/cache";

export async function deleteAdmin(id: string) {
  await checkAdminAuth();

  const adminToDelete = await db.user.findUnique({
    where: { id },
    select: { email: true },
  });

  if (adminToDelete?.email === "<EMAIL>") {
    throw new Error("Não é possível excluir o administrador principal");
  }

  await db.user.delete({
    where: { id },
  });

  revalidatePath("/admin");
}
