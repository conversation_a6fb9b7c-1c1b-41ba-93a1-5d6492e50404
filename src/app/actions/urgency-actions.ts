"use server";

import { db } from "@/lib/db";
import { checkAdminAuth } from "@/lib/auth";
import { UrgencyFormData } from "@/lib/types/urgency";
import { revalidatePath } from "next/cache";

export async function createUrgency(data: UrgencyFormData): Promise<void> {
  await checkAdminAuth();

  await db.urgency.create({
    data: {
      name: data.name,
    },
  });

  revalidatePath("/admin/urgencias");
}

export async function updateUrgency(
  id: string,
  data: UrgencyFormData
): Promise<void> {
  await checkAdminAuth();

  await db.urgency.update({
    where: { id },
    data: {
      name: data.name,
    },
  });

  revalidatePath("/admin/urgencias");
}

export async function deleteUrgency(id: string): Promise<void> {
  await checkAdminAuth();

  await db.urgency.delete({
    where: { id },
  });

  revalidatePath("/admin/urgencias");
}

export async function getUrgency(id: string) {
  await checkAdminAuth();

  return db.urgency.findUnique({
    where: { id },
  });
}

export async function getUrgencies(page: number, pageSize: number, name?: string) {
  await checkAdminAuth();

  const where = name ? {
    name: {
      contains: name,
      mode: 'insensitive' as const
    }
  } : {};

  const [urgencies, total] = await Promise.all([
    db.urgency.findMany({
      where,
      take: pageSize,
      skip: (page - 1) * pageSize,
      orderBy: { name: "asc" },
    }),
    db.urgency.count({ where }),
  ]);

  return { urgencies, total };
}

export async function getAllUrgencies() {
  return db.urgency.findMany({
    orderBy: { name: "asc" },
  });
}
