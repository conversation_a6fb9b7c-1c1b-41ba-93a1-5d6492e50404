"use server";

import { db } from "@/lib/db";
import { checkAdminAuth } from "@/lib/auth";
import { SpecialtyFormData } from "@/lib/types/specialty";
import { revalidatePath } from "next/cache";
import { Prisma } from "@prisma/client";

export async function createSpecialty(data: SpecialtyFormData): Promise<void> {
  await checkAdminAuth();

  await db.specialty.create({
    data: {
      name: data.name,
    },
  });

  revalidatePath("/admin/especialidades");
}

export async function updateSpecialty(
  id: string,
  data: SpecialtyFormData
): Promise<void> {
  await checkAdminAuth();

  await db.specialty.update({
    where: { id },
    data: {
      name: data.name,
    },
  });

  revalidatePath("/admin/especialidades");
}

export async function deleteSpecialty(id: string): Promise<void> {
  await checkAdminAuth();

  // Primeiro, deletamos todos os relacionamentos na tabela user_specialties
  await db.userSpecialty.deleteMany({
    where: {
      specialtyId: id,
    },
  });

  // Depois, deletamos a especialidade
  await db.specialty.delete({
    where: { id },
  });

  revalidatePath("/admin/especialidades");
}

export async function getSpecialty(id: string) {
  await checkAdminAuth();

  return db.specialty.findUnique({
    where: { id },
  });
}

export async function getSpecialties(page: number, pageSize: number, name?: string) {
  await checkAdminAuth();

  const whereClause = name
    ? {
        name: {
          contains: name,
          mode: Prisma.QueryMode.insensitive,
        },
      }
    : {};

  const [specialties, total] = await Promise.all([
    db.specialty.findMany({
      where: whereClause,
      take: pageSize,
      skip: (page - 1) * pageSize,
      orderBy: { name: "asc" },
    }),
    db.specialty.count({
      where: whereClause,
    }),
  ]);

  return { specialties, total };
}

export async function getAllSpecialties() {
  return db.specialty.findMany({
    orderBy: { name: "asc" },
  });
}
