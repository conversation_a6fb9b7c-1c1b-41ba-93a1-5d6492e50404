"use server";

import { db } from "@/lib/db";
import { revalidatePath } from "next/cache";
import { checkAuth } from "@/lib/auth";

export async function getCampaignFilters(campaignId: string) {
  return db.campaign.findUnique({
    where: { id: campaignId },
    select: {
      filterState: true,
      filterAlteracoes: true,
    },
  });
}

export async function updateCampaignFilters(
  campaignId: string,
  state: string,
  alteracaoIds: number[]
) {
  const campaign = await db.campaign.update({
    where: { id: campaignId },
    data: {
      filterState: state,
      filterAlteracoes: alteracaoIds,
    },
  });

  revalidatePath(`/admin/campanhas/${campaignId}`);
  return campaign;
}

export async function filtrarPacientes(filters: {
  state?: string;
  alteracaoIds?: number[];
}) {
  const where: any = {
    type: "patient",
    ...(filters.state && filters.state !== "todos" && { state: filters.state }),
    ...(filters.alteracaoIds?.length && {
      UserChange: {
        some: {
          changeId: { in: filters.alteracaoIds },
        },
      },
    }),
  };

  return db.user.findMany({
    where,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      whatsapp: true,
      city: true,
      state: true,
    },
  });
}

// Adicionando os novos métodos necessários
export async function getProfessionalChanges() {
  const session = await checkAuth();
  return db.profissionalChange.findMany({
    where: {
      userId: session.user.id,
    },
  });
}

export async function updateProfessionalChanges(changes: number[]) {
  const session = await checkAuth();
  const userId = session.user.id;

  return db.$transaction(async (tx) => {
    // Remove alterações antigas
    await tx.profissionalChange.deleteMany({
      where: { userId },
    });

    // Cria novas alterações
    const result = await Promise.all(
      changes.map((changeId) =>
        tx.profissionalChange.create({
          data: {
            userId,
            changeId,
          },
        })
      )
    );

    return { success: true, result };
  });
}
