"use server";

import { db } from "@/lib/db";
import { checkAuth } from "@/lib/auth";
import fs from "fs";
import path from "path";

// Interface para as alterações do arquivo JSON
interface AlteracaoJson {
  id: number;
  nome: string;
  descricao: string;
  created_at: string;
  updated_at: string;
  url: string;
}

// Função para carregar as alterações do arquivo JSON
async function loadAlteracoes(): Promise<Record<number, string>> {
  try {
    const filePath = path.join(
      process.cwd(),
      "public",
      "assets",
      "mocks",
      "alteracoes.json"
    );
    const fileContent = await fs.promises.readFile(filePath, "utf8");
    const alteracoes: AlteracaoJson[] = JSON.parse(fileContent);

    // Criar um mapeamento de ID para nome
    const alteracoesMap: Record<number, string> = {};
    alteracoes.forEach((alteracao) => {
      alteracoesMap[alteracao.id] = alteracao.nome;
    });

    return alteracoesMap;
  } catch (error) {
    console.error("Erro ao carregar alterações do arquivo JSON:", error);
    return {}; // Retorna um objeto vazio em caso de erro
  }
}

export async function getUserChanges() {
  const session = await checkAuth();
  return db.userChange.findMany({
    where: {
      userId: session.user.id,
    },
  });
}

export async function updateUserChanges(changes: number[]) {
  const session = await checkAuth();
  const userId = session.user.id;

  return db.$transaction(async (tx) => {
    // Deletar alterações existentes
    await tx.userChange.deleteMany({
      where: { userId },
    });

    // Criar novas alterações
    return Promise.all(
      changes.map((changeId) =>
        tx.userChange.create({
          data: {
            userId,
            changeId,
          },
        })
      )
    );
  });
}

// Nova função para obter alterações formatadas para exibição
export async function getUserChangesFormatted() {
  const userChanges = await getUserChanges();

  // Carregar os nomes das alterações do arquivo JSON
  const alteracoesMap = await loadAlteracoes();

  // Formatar os dados para retornar apenas o necessário
  return userChanges.map((uc) => ({
    id: uc.changeId,
    nome: alteracoesMap[uc.changeId] || `Alteração ${uc.changeId}`,
  }));
}
