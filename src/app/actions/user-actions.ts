"use server";

import { db } from "@/lib/db";
import { checkAuth } from "@/lib/auth";

export async function getUserProfile() {
  const session = await checkAuth();

  const user = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      city: true,
      state: true,
      whatsapp: true,
    },
  });

  if (!user) {
    throw new Error("Usuário não encontrado");
  }

  return {
    id: user.id,
    nome: `${user.firstName} ${user.lastName}`.trim(),
    email: user.email,
    cidade: user.city,
    estado: user.state,
    whatsapp: user.whatsapp,
  };
}
