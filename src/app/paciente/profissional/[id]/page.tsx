"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Home } from "lucide-react";
import { GoogleMap, Marker, useLoadScript } from "@react-google-maps/api";
import { useEffect, useState, use } from "react";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, Clock } from "lucide-react";
import Link from "next/link";
import { Loader } from "@/components/ui/loader";
import {
  registerDentistVisit,
  registerDentistContact,
} from "@/app/actions/dentist-actions";
import { getUserChangesFormatted } from "@/app/actions/user-changes";
import { getUserProfile } from "@/app/actions/user-actions";

interface Profissional {
  id: string;
  nome: string;
  foto: string;
  especialidades: string[];
  urgencia: string[];
  latitude: number;
  longitude: number;
  cidade: string;
  uf: string;
  telefone?: string;
  instagram?: string;
  avaliacao?: number;
  horarioAtendimento?: string;
  sobre?: string;
}

interface Usuario {
  id: string;
  nome: string;
  email: string;
  cidade?: string;
  estado?: string;
  whatsapp?: string;
}

interface Alteracao {
  id: number;
  nome: string;
}

export default function PerfilProfissional({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Usar React.use para desembrulhar a Promise params
  const { id } = use(params);

  const [profissional, setProfissional] = useState<Profissional | null>(null);
  const [usuario, setUsuario] = useState<Usuario | null>(null);
  const [visitaRegistrada, setVisitaRegistrada] = useState(false);
  const [alteracoesUsuario, setAlteracoesUsuario] = useState<Alteracao[]>([]);
  const [loading, setLoading] = useState(true);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Carregar dados do profissional
        const responseProfissional = await fetch(`/api/profissionais/${id}`);
        if (!responseProfissional.ok)
          throw new Error("Falha ao carregar dados do profissional");
        const dataProfissional = await responseProfissional.json();
        setProfissional(dataProfissional);

        // Carregar dados do usuário logado usando server action
        try {
          const userProfile = await getUserProfile();
          setUsuario(userProfile);
        } catch (error) {
          console.error("Erro ao carregar perfil do usuário:", error);
        }

        // Carregar alterações do usuário usando server action
        try {
          const alteracoes = await getUserChangesFormatted();
          setAlteracoesUsuario(alteracoes);
        } catch (error) {
          console.error("Erro ao carregar alterações do usuário:", error);
        }

        // Registrar visita apenas uma vez usando a server action
        if (!visitaRegistrada) {
          await registerDentistVisit(id);
          setVisitaRegistrada(true);
        }
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, visitaRegistrada]);

  const handleWhatsApp = async () => {
    if (!profissional?.telefone || !usuario?.nome) return;

    // Registrar o contato antes de abrir o WhatsApp
    await registerDentistContact(id);

    // Construir a mensagem com base nas alterações encontradas
    let message = "";

    if (alteracoesUsuario.length > 0) {
      // Mensagem para paciente com alterações
      message = `Olá, meu nome é ${usuario.nome}, sou de ${
        usuario.cidade || profissional.cidade
      }, e realizei o autoexame pelo site.\nEncontrei as seguintes alterações que me deixaram em dúvida ou preocupado(a):\n\n`;

      // Formatar a lista de alterações com regras gramaticais corretas
      if (alteracoesUsuario.length === 1) {
        // Uma única alteração
        message += `${alteracoesUsuario[0].nome}`;
      } else if (alteracoesUsuario.length === 2) {
        // Duas alterações separadas por "e"
        message += `${alteracoesUsuario[0].nome} e ${alteracoesUsuario[1].nome}`;
      } else {
        // Mais de duas alterações: separadas por vírgula e a última com "e"
        const ultimaAlteracao = alteracoesUsuario[alteracoesUsuario.length - 1];
        const alteracoesAnteriores = alteracoesUsuario.slice(0, -1);

        message += alteracoesAnteriores.map((alt) => alt.nome).join(", ");
        message += ` e ${ultimaAlteracao.nome}`;
      }

      // Adicionar reticências após a lista de alterações
      message += ".\n\nGostaria de agendar uma avaliação.";
    } else {
      // Mensagem para paciente sem alterações
      message = `Olá, meu nome é ${usuario.nome}, sou de ${
        usuario.cidade || profissional.cidade
      } e realizei o autoexame pelo site.\nNão encontrei nenhuma alteração, mas gostaria de fazer um check-up preventivo com um dentista.`;
    }

    window.open(
      `https://wa.me/${profissional.telefone}?text=${encodeURIComponent(
        message
      )}`,
      "_blank"
    );
  };

  const handleInstagram = () => {
    if (!profissional?.instagram) return;

    const instagramHandle = profissional.instagram.replace("@", "");
    window.open(`https://instagram.com/${instagramHandle}`, "_blank");
  };

  if (!isLoaded || !profissional) {
    return (
      <div className="relative w-full h-screen bg-[#f1f1f1]">
        <Loader isLoading={true} />
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-white">
      {/* Header - Ajustado para ter a largura do card e fundo transparente */}
      <div className="p-4 md:p-6 flex justify-center">
        <div className="w-full max-w-3xl">
          {/* Header com a mesma largura do card */}
          <div className="flex items-center justify-between w-full mb-4">
            <Button
              variant="ghost"
              className="text-blue-600 hover:bg-blue-50 cursor-pointer hover:text-blue-700"
              onClick={() => window.history.back()}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M19 12H5M12 19l-7-7 7-7" />
              </svg>
            </Button>
            <h1 className="text-blue-600 text-xl font-medium">
              Perfil do Dentista
            </h1>
            <div className="flex gap-2">
              <Link href="/paciente/home">
                <Button
                  variant="ghost"
                  className="text-blue-600 hover:bg-blue-50 cursor-pointer hover:text-blue-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                    <polyline points="9 22 9 12 15 12 15 22" />
                  </svg>
                </Button>
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-3xl shadow-lg overflow-hidden">
            <div className="bg-gradient-to-r from-[#0368df] to-[#1f51d1] rounded-t-3xl p-4 md:p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                  <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden bg-white mx-auto md:mx-0">
                    <img
                      src={profissional.foto || "/assets/img/robo-feliz.png"}
                      alt={profissional.nome}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="text-center md:text-left">
                    <h1 className="text-xl md:text-2xl font-bold text-white">
                      Dr(a). {profissional.nome}
                    </h1>
                    <div className="flex flex-wrap justify-center md:justify-start gap-1 mt-1">
                      {(profissional?.especialidades?.length || 0) > 0 && (
                        <div className="flex flex-wrap justify-center md:justify-start gap-1 mt-1">
                          {profissional.especialidades.map(
                            (esp: string, index: number) => (
                              <span
                                key={index}
                                className="text-sm text-white opacity-90"
                              >
                                {esp}
                                {index <
                                  profissional.especialidades.length - 1 &&
                                  ", "}
                              </span>
                            )
                          )}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center justify-center md:justify-start gap-2 mt-2 text-white opacity-90">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm">
                        {profissional.cidade} - {profissional.uf}
                      </span>
                    </div>
                  </div>
                </div>

                {(profissional?.urgencia?.length || 0) > 0 && (
                  <div className="flex flex-col items-center md:items-end">
                    <h2 className="font-semibold text-white mb-2">
                      Atendimentos
                    </h2>
                    <div className="flex flex-wrap gap-2 justify-center md:justify-end">
                      {profissional.urgencia.map((urg, index) => {
                        // Converte o valor para o formato de horas
                        const horasAtendimento =
                          urg === "1" ? "12 h" : urg === "2" ? "24 h" : urg;

                        return (
                          <Badge
                            key={index}
                            variant="outline"
                            className="border-white text-white"
                          >
                            {horasAtendimento}
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Map Section */}
            <div className="p-4 md:p-6">
              <div className="w-full h-[300px] md:h-[400px]">
                <GoogleMap
                  zoom={15}
                  center={{
                    lat: profissional.latitude,
                    lng: profissional.longitude,
                  }}
                  mapContainerClassName="w-full h-full rounded-lg"
                  options={{
                    zoomControl: false,
                    streetViewControl: false,
                    mapTypeControl: false,
                    fullscreenControl: false,
                  }}
                >
                  <Marker
                    position={{
                      lat: profissional.latitude,
                      lng: profissional.longitude,
                    }}
                    icon={{
                      url: profissional.foto || "/assets/img/robo-feliz.png",
                      scaledSize: new google.maps.Size(40, 40),
                      anchor: new google.maps.Point(20, 20),
                    }}
                  />
                </GoogleMap>
              </div>

              {/* Profile Info Section */}
              <div className="mt-6 space-y-4">
                <div className="flex items-start justify-between">
                  {profissional.avaliacao && (
                    <div className="flex items-center gap-1">
                      <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold">
                        {profissional.avaliacao}
                      </span>
                    </div>
                  )}
                </div>

                {profissional.horarioAtendimento && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">
                      {profissional.horarioAtendimento}
                    </span>
                  </div>
                )}

                {profissional.sobre && (
                  <div>
                    <h2 className="font-semibold text-gray-900 mb-2">Sobre</h2>
                    <p className="text-gray-600 text-sm">
                      {profissional.sobre}
                    </p>
                  </div>
                )}

                {/* Contact Buttons */}
                <div className="mt-6 space-y-3">
                  {profissional?.telefone && (
                    <Button
                      className="w-full h-12 text-white font-bold text-lg bg-[#25D366] hover:bg-[#1ea855] gap-2"
                      onClick={handleWhatsApp}
                      disabled={!usuario}
                    >
                      <img
                        src="/whatsapp.svg"
                        alt="WhatsApp"
                        width={24}
                        height={24}
                        className="text-white"
                      />
                      Marcar Consulta
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
