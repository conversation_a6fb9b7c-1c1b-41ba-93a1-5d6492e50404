"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/login");
      return;
    }

    // Redireciona baseado no tipo do usuário
    switch ((session.user as any).type) {
      case "patient":
        router.push("/paciente/home");
        break;
      case "dentist":
        router.push("/dentista/home");
        break;
      case "admin":
        router.push("/admin");
        break;
      default:
        router.push("/login");
    }
  }, [session, status, router]);

  return (
    <main className="min-h-screen flex items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
    </main>
  );
}
