import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { updateUserChanges } from "@/app/actions/queries";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { changes } = await request.json();
    const userId = (session.user as any).id;

    const result = await updateUserChanges(changes);
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao atualizar alterações" },
      { status: 500 }
    );
  }
}
