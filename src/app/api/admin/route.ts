import { NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

// GET - Lista todos os admins
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const name = searchParams.get("name") || "";
    const email = searchParams.get("email") || "";
    const skip = (page - 1) * pageSize;

    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const whereClause = {
      type: "admin",
      ...(name && {
        firstName: {
          contains: name,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
      ...(email && {
        email: {
          contains: email,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
    };

    const [total, items] = await Promise.all([
      db.user.count({
        where: whereClause,
      }),
      db.user.findMany({
        where: whereClause,
        select: {
          id: true,
          firstName: true,
          email: true,
        },
        skip,
        take: pageSize,
        orderBy: {
          firstName: "asc",
        },
      }),
    ]);

    return NextResponse.json({
      items,
      total,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("Erro ao buscar administradores:", error);
    return NextResponse.json(
      { error: "Erro ao buscar administradores" },
      { status: 500 }
    );
  }
}

// POST - Cria novo admin
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { firstName, email, password } = await request.json();
    const hashedPassword = await hash(password, 10);

    const admin = await db.user.create({
      data: {
        firstName,
        lastName: "Admin", // Valor padrão para admin
        email,
        hashedPassword,
        type: "admin",
        // Campos obrigatórios adicionais
        whatsapp: "00000000000", // Valor padrão para admin
        zipCode: "00000000", // Valor padrão para admin
        state: "SP", // Valor padrão para admin
        city: "São Paulo", // Valor padrão para admin
      },
    });

    return NextResponse.json(admin);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao criar administrador" },
      { status: 500 }
    );
  }
}
