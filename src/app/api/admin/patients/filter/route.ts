import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const state = searchParams.get("state");
    const alteracaoIds = searchParams.getAll("alteracaoIds[]").map(Number);

    const where: any = {
      type: "patient",
    };

    if (state && state !== "todos") {
      where.state = state;
    }

    if (alteracaoIds.length > 0) {
      where.UserChange = {
        // Alterado de 'alteracoes' para 'UserChange'
        some: {
          changeId: {
            in: alteracaoIds,
          },
        },
      };
    }

    const patients = await db.user.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        whatsapp: true,
        city: true,
        state: true,
      },
    });

    return NextResponse.json(patients);
  } catch (error) {
    console.error("Erro ao filtrar pacientes:", error); // Adicionado log do erro
    return NextResponse.json(
      { error: "Erro ao filtrar pacientes" },
      { status: 500 }
    );
  }
}
