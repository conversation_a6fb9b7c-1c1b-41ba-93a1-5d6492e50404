import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { hash } from "bcryptjs";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { patients } = await req.json();

    let imported = 0;
    let skipped = 0;
    let errors: string[] = [];

    for (const patient of patients) {
      if (!patient.email) {
        errors.push("Email não fornecido");
        continue;
      }

      try {
        // Log para debug
        console.log("Processando paciente:", patient);

        // Verifica se já existe um paciente com este email
        const existingPatient = await db.user.findUnique({
          where: { email: patient.email },
        });

        if (existingPatient) {
          skipped++;
          continue;
        }

        // Processa o nome completo
        const nameParts = String(patient.name).trim().split(/\s+/);
        const firstName = nameParts[0];
        const lastName = nameParts.slice(1).join(" ");

        // Converte a senha para string e faz o hash
        const password = String(patient.password);
        const hashedPassword = await hash(password, 12);

        // Converte o whatsapp para string e remove caracteres não numéricos
        const whatsapp = String(patient.whatsapp || "").replace(/\D/g, "");

        // Log para debug
        console.log("Dados processados:", {
          email: patient.email,
          firstName,
          lastName,
          whatsapp,
          passwordLength: password.length,
        });

        // Cria o novo paciente
        const newPatient = await db.user.create({
          data: {
            email: patient.email,
            firstName,
            lastName: lastName || "",
            whatsapp,
            hashedPassword,
            type: "patient",
            zipCode: "00000000",
            state: "SP",
            city: "São Paulo",
          },
        });

        console.log(`Paciente criado com sucesso: ${newPatient.email}`);
        imported++;
      } catch (error) {
        console.error("Erro ao processar paciente:", {
          paciente: patient,
          erro: error,
        });
        errors.push(`Erro ao processar ${patient.email}: ${error}`);
        continue;
      }
    }

    return NextResponse.json({
      success: imported > 0,
      imported,
      skipped,
      errors,
      message: `${imported} pacientes importados com sucesso. ${skipped} pacientes ignorados.${
        errors.length > 0 ? ` Erros: ${errors.join(", ")}` : ""
      }`,
    });
  } catch (error) {
    console.error("Erro na importação:", error);
    return NextResponse.json(
      { error: "Erro ao processar importação", details: String(error) },
      { status: 500 }
    );
  }
}
