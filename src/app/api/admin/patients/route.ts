import { NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

// GET - Lista todos os pacientes
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const name = searchParams.get("name") || "";
    const email = searchParams.get("email") || "";
    const whatsapp = searchParams.get("whatsapp") || "";
    const skip = (page - 1) * pageSize;

    const whereClause = {
      type: "patient",
      ...(name && {
        OR: [
          {
            firstName: {
              contains: name,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            lastName: {
              contains: name,
              mode: Prisma.QueryMode.insensitive,
            },
          },
        ],
      }),
      ...(email && {
        email: {
          contains: email,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
      ...(whatsapp && {
        whatsapp: {
          contains: whatsapp,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
    };

    const [total, items] = await Promise.all([
      db.user.count({
        where: whereClause,
      }),
      db.user.findMany({
        where: whereClause,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          whatsapp: true,
        },
        skip,
        take: pageSize,
        orderBy: {
          firstName: "asc",
        },
      }),
    ]);

    return NextResponse.json({
      items,
      total,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("Erro ao buscar pacientes:", error);
    return NextResponse.json(
      { error: "Erro ao buscar pacientes" },
      { status: 500 }
    );
  }
}

// POST - Cria novo paciente
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const data = await request.json();
    const hashedPassword = await hash(data.password, 10);

    const patient = await db.user.create({
      data: {
        ...data,
        hashedPassword,
        type: "patient",
      },
    });

    return NextResponse.json(patient);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao criar paciente" },
      { status: 500 }
    );
  }
}
