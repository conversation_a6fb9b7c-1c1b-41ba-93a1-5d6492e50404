import { NextResponse } from "next/server";
import { updatePaciente } from "@/app/actions/queries";
import type { RouteParams } from "@/types/route";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function PUT(request: Request, context: RouteParams) {
  try {
    const { id } = await context.params;
    const data = await request.json();

    const patient = await updatePaciente(id, data);
    return NextResponse.json(patient);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao atualizar paciente" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: RouteParams
) {
  try {
    const { id } = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Primeiro, remove todos os registros relacionados
    await db.$transaction([
      // Remove as alterações do paciente
      db.userChange.deleteMany({
        where: { userId: id },
      }),
      // Remove as especialidades do paciente
      db.userSpecialty.deleteMany({
        where: { userId: id },
      }),
      // Remove as visitas do paciente
      db.dentistVisit.deleteMany({
        where: { patientId: id },
      }),
      // Remove os contatos do paciente
      db.dentistContact.deleteMany({
        where: { patientId: id },
      }),
      // Remove as alterações profissionais
      db.profissionalChange.deleteMany({
        where: { userId: id },
      }),
      // Por fim, remove o usuário
      db.user.delete({
        where: { id },
      }),
    ]);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao deletar paciente:", error);
    return NextResponse.json(
      { error: "Erro ao deletar paciente" },
      { status: 500 }
    );
  }
}
