import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { createCampaign } from "@/app/actions/queries";
import { CampaignType } from "@prisma/client";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Não autorizado" },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const name = searchParams.get("name") || "";
    const type = searchParams.get("type") || "";
    const createdAt = searchParams.get("createdAt");
    const lastSentAt = searchParams.get("lastSentAt");

    const where = {
      ...(name && {
        name: {
          contains: name,
          mode: "insensitive" as const,
        },
      }),
      ...(type && {
        type: type as CampaignType,
      }),
      ...(createdAt && {
        createdAt: {
          gte: new Date(createdAt),
          lt: new Date(new Date(createdAt).setDate(new Date(createdAt).getDate() + 1)),
        },
      }),
      ...(lastSentAt && {
        lastSentAt: {
          gte: new Date(lastSentAt),
          lt: new Date(new Date(lastSentAt).setDate(new Date(lastSentAt).getDate() + 1)),
        },
      }),
    };

    const [campaigns, total] = await Promise.all([
      prisma.campaign.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      prisma.campaign.count({ where }),
    ]);

    return NextResponse.json({
      items: campaigns,
      total,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("Erro ao buscar campanhas:", error);
    return NextResponse.json(
      { error: "Erro ao buscar campanhas" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const campaign = await createCampaign(data);
    return NextResponse.json(campaign);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao criar campanha" },
      { status: 500 }
    );
  }
}
