import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { sendEmail } from "@/lib/email/index";

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function POST(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Buscar a campanha com seus filtros e template
    const campaign = await db.campaign.findUnique({
      where: { id: params.id },
      include: {
        emailTemplate: true,
      },
    });

    if (!campaign) {
      return NextResponse.json(
        { error: "Campanha não encontrada" },
        { status: 404 }
      );
    }

    if (!campaign.emailTemplate) {
      return NextResponse.json(
        { error: "Template de email não encontrado" },
        { status: 404 }
      );
    }

    // Buscar pacientes baseado nos filtros da campanha
    const patients = await db.user.findMany({
      where: {
        type: "patient",
        ...(campaign.filterState && { state: campaign.filterState }),
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    });

    // Enviar emails para cada paciente
    const results = await Promise.allSettled(
      patients.map((patient) => {
        const personalizedHtml = campaign.emailTemplate.template.replace(
          /\{nome\}/g,
          `${patient.firstName} ${patient.lastName}`
        );

        return sendEmail({
          to: patient.email,
          subject: campaign.emailTemplate.title,
          html: personalizedHtml,
        });
      })
    );

    // Contar sucessos e falhas
    const successful = results.filter((r) => r.status === "fulfilled").length;
    const failed = results.filter((r) => r.status === "rejected").length;

    // Atualizar lastSentAt da campanha
    await db.campaign.update({
      where: { id: params.id },
      data: { lastSentAt: new Date() },
    });

    return NextResponse.json({
      success: true,
      total: patients.length,
      successful,
      failed,
    });
  } catch (error) {
    console.error("Erro ao enviar emails:", error);
    return NextResponse.json(
      { error: "Erro ao enviar emails" },
      { status: 500 }
    );
  }
}
