import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function GET(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const campaign = await db.campaign.findUnique({
      where: { id: params.id },
      select: {
        filterEspecialidades: true,
      },
    });

    if (!campaign) {
      return NextResponse.json(
        { error: "Campanha não encontrada" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      filterEspecialidades: campaign.filterEspecialidades || [],
    });
  } catch (error) {
    console.error("Erro ao carregar filtros:", error);
    return NextResponse.json(
      { error: "Erro ao carregar filtros" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { especialidadeIds } = await request.json();

    const updatedCampaign = await db.campaign.update({
      where: { id: params.id },
      data: {
        filterEspecialidades: especialidadeIds,
      },
      select: {
        id: true,
        filterEspecialidades: true,
      },
    });

    return NextResponse.json(updatedCampaign);
  } catch (error) {
    console.error("Erro ao salvar filtros:", error);
    return NextResponse.json(
      { error: "Erro ao salvar filtros" },
      { status: 500 }
    );
  }
}
