import { NextResponse } from "next/server";
import { updateCampaignFilters } from "@/app/actions/queries";
import type { RouteParams } from "@/types/route";

interface RouteContext {
  params: Promise<{ id: string }>;
}

export async function POST(request: Request, context: RouteContext) {
  try {
    const { id } = await context.params;
    const { state, alteracaoIds } = await request.json();

    const campaign = await updateCampaignFilters(id, state, alteracaoIds);
    return NextResponse.json(campaign);
  } catch (error) {
    console.error("Erro ao salvar filtros:", error);
    return NextResponse.json(
      { error: "Erro ao salvar filtros" },
      { status: 500 }
    );
  }
}
