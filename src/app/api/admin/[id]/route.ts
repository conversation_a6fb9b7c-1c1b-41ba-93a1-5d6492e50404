import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hash } from "bcryptjs";

interface RouteParams {
  params: Promise<{ id: string }>;
}

// GET - Busca admin específico
export async function GET(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { id } = await params;
    const admin = await db.user.findUnique({
      where: { id },
      select: {
        id: true,
        firstName: true,
        email: true,
      },
    });

    if (!admin) {
      return NextResponse.json(
        { error: "Administrador não encontrado" },
        { status: 404 }
      );
    }

    return NextResponse.json(admin);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao buscar administrador" },
      { status: 500 }
    );
  }
}

// PUT - Atualiza admin
export async function PUT(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { id } = await params;
    const { firstName, email, password } = await request.json();

    const updateData: any = {
      firstName,
      email,
    };

    if (password) {
      updateData.hashedPassword = await hash(password, 10);
    }

    const admin = await db.user.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json(admin);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao atualizar administrador" },
      { status: 500 }
    );
  }
}

// DELETE - Remove admin
export async function DELETE(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { id } = await params;
    await db.user.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao excluir administrador" },
      { status: 500 }
    );
  }
}
