import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Prisma, Template<PERSON>ey } from "@prisma/client";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const key = searchParams.get("key") || "";
    const title = searchParams.get("title") || "";

    const where = {
      ...(key && key !== "all" && {
        key: key as Template<PERSON><PERSON>,
      }),
      ...(title && {
        title: {
          contains: title,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
    };

    const [templates, total] = await Promise.all([
      db.emailTemplate.findMany({
        where,
        select: {
          id: true,
          key: true,
          title: true,
          template: true,
          createdAt: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: "desc" },
      }),
      db.emailTemplate.count({ where }),
    ]);

    return NextResponse.json({
      items: templates,
      total,
      page,
      pageSize,
    });
  } catch (error) {
    console.error("Erro ao carregar templates:", error);
    return NextResponse.json(
      { error: "Erro ao carregar templates" },
      { status: 500 }
    );
  }
}
