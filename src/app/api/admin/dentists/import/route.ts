import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { hash } from "bcryptjs";

export async function POST(req: Request) {
  try {
    const { dentists } = await req.json();

    let imported = 0;
    let skipped = 0;
    let errors: string[] = [];

    for (const dentist of dentists) {
      if (!dentist.email) {
        errors.push("Email não fornecido");
        continue;
      }

      try {
        // Log para debug
        console.log("Processando dentista:", dentist);

        // Verifica se já existe um dentista com este email
        const existingDentist = await db.user.findUnique({
          where: { email: dentist.email },
        });

        if (existingDentist) {
          skipped++;
          continue;
        }

        // Processa o nome completo
        const nameParts = String(dentist.name).trim().split(/\s+/);
        const firstName = nameParts[0];
        const lastName = nameParts.slice(1).join(" ");

        // Converte a senha para string e faz o hash
        const password = String(dentist.password);
        const hashedPassword = await hash(password, 12);

        // Converte o whatsapp para string e remove caracteres não numéricos
        const whatsapp = String(dentist.whatsapp || "").replace(/\D/g, "");

        // Log para debug
        console.log("Dados processados:", {
          email: dentist.email,
          firstName,
          lastName,
          whatsapp,
          passwordLength: password.length,
        });

        // Cria o novo dentista
        const newDentist = await db.user.create({
          data: {
            email: dentist.email,
            firstName,
            lastName: lastName || "",
            whatsapp,
            hashedPassword,
            type: "dentist",
            zipCode: "00000000",
            state: "SP",
            city: "São Paulo",
          },
        });

        console.log(`Dentista criado com sucesso: ${newDentist.email}`);
        imported++;
      } catch (error) {
        console.error("Erro ao processar dentista:", {
          dentista: dentist,
          erro: error,
        });
        errors.push(`Erro ao processar ${dentist.email}: ${error}`);
        continue;
      }
    }

    return NextResponse.json({
      success: imported > 0,
      imported,
      skipped,
      errors,
      message: `${imported} dentistas importados com sucesso. ${skipped} dentistas ignorados.${
        errors.length > 0 ? ` Erros: ${errors.join(", ")}` : ""
      }`,
    });
  } catch (error) {
    console.error("Erro na importação:", error);
    return NextResponse.json(
      { error: "Erro ao processar importação", details: String(error) },
      { status: 500 }
    );
  }
}
