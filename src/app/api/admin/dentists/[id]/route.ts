import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { updateDentista } from "@/app/actions/queries";
import type { RouteParams } from "@/types/route";

// Atualize a interface RouteParams no arquivo @/types/route.ts
interface RouteContext {
  params: Promise<{ id: string }>;
}

// GET - Obtém um dentista específico
export async function GET(request: Request, context: RouteContext) {
  try {
    const { id } = await context.params;
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const dentist = await db.user.findUnique({
      where: { id },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        cpf: true,
        whatsapp: true,
        zipCode: true,
        state: true,
        city: true,
        type: true,
        cro: true,
        croState: true,
        address: true,
        number: true,
        complement: true,
        district: true,
        image: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(dentist);
  } catch (error) {
    console.error("Erro ao buscar dentista:", error);
    return NextResponse.json(
      { error: "Erro ao buscar dentista" },
      { status: 500 }
    );
  }
}

// PUT - Atualiza um dentista
export async function PUT(request: Request, context: RouteContext) {
  try {
    const { id } = await context.params;
    const data = await request.json();

    const updatedDentist = await updateDentista(id, data);
    return NextResponse.json(updatedDentist);
  } catch (error) {
    console.error("Erro ao atualizar dentista:", error);
    return NextResponse.json(
      { error: "Erro ao atualizar dentista" },
      { status: 500 }
    );
  }
}

// DELETE - Remove um dentista
export async function DELETE(request: Request, context: RouteContext) {
  try {
    const { id } = await context.params;
    const session = await getServerSession(authOptions);
    
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Primeiro, remove todos os registros relacionados
    await db.$transaction([
      // Remove as especialidades do dentista
      db.userSpecialty.deleteMany({
        where: { userId: id },
      }),
      // Remove o usuário
      db.user.delete({
        where: { id },
      }),
    ]);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao deletar dentista:", error);
    return NextResponse.json(
      { error: "Erro ao deletar dentista" },
      { status: 500 }
    );
  }
}
