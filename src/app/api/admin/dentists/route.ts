import { NextResponse } from "next/server";
import { getDentistas } from "@/app/actions/queries";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { hash } from "bcryptjs";
import { Prisma } from "@prisma/client";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const name = searchParams.get("name") || "";
    const email = searchParams.get("email") || "";
    const cro = searchParams.get("cro") || "";
    const croState = searchParams.get("croState") || "";
    const skip = (page - 1) * pageSize;

    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const whereClause = {
      type: "dentist",
      ...(name && {
        OR: [
          {
            firstName: {
              contains: name,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            lastName: {
              contains: name,
              mode: Prisma.QueryMode.insensitive,
            },
          },
        ],
      }),
      ...(email && {
        email: {
          contains: email,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
      ...(cro && {
        cro: {
          contains: cro,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
      ...(croState && {
        croState: {
          contains: croState,
          mode: Prisma.QueryMode.insensitive,
        },
      }),
    };

    const [total, items] = await Promise.all([
      db.user.count({
        where: whereClause,
      }),
      db.user.findMany({
        where: whereClause,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          cro: true,
          croState: true,
        },
        skip,
        take: pageSize,
        orderBy: {
          firstName: "asc",
        },
      }),
    ]);

    return NextResponse.json({
      items,
      total,
      page,
      pageSize,
    });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes("Não autenticado")) {
        return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
      }
      if (error.message.includes("Não autorizado")) {
        return NextResponse.json({ error: "Não autorizado" }, { status: 403 });
      }
    }
    return NextResponse.json(
      { error: "Erro ao carregar dentistas" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const data = await request.json();
    const hashedPassword = await hash(data.password, 12);

    const newDentist = await db.user.create({
      data: {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        hashedPassword,
        whatsapp: data.whatsapp,
        cpf: data.cpf,
        cro: data.cro,
        croState: data.croState,
        zipCode: data.zipCode,
        state: data.state,
        city: data.city,
        address: data.address,
        number: data.number,
        complement: data.complement,
        district: data.district,
        type: "dentist",
      },
    });

    return NextResponse.json(newDentist);
  } catch (error) {
    console.error("Erro ao criar dentista:", error);
    return NextResponse.json(
      { error: "Erro ao criar dentista" },
      { status: 500 }
    );
  }
}
