import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || (session.user as any).type !== "admin") {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const especialidadeIds = searchParams.getAll("especialidadeIds[]");

    // Se não houver especialidades selecionadas, retorna todos os dentistas
    if (!especialidadeIds.length) {
      const allDentists = await db.user.findMany({
        where: { type: "dentist" },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          whatsapp: true,
          city: true,
          state: true,
        },
      });
      return NextResponse.json(allDentists);
    }

    // Busca dentistas que têm pelo menos uma das especialidades selecionadas
    const dentists = await db.user.findMany({
      where: {
        type: "dentist",
        specialties: {
          some: {
            specialtyId: {
              in: especialidadeIds,
            },
          },
        },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        whatsapp: true,
        city: true,
        state: true,
        cro: true,
        croState: true,
      },
    });

    return NextResponse.json(dentists);
  } catch (error) {
    console.error("Erro ao filtrar dentistas:", error);
    return NextResponse.json(
      { error: "Erro ao filtrar dentistas" },
      { status: 500 }
    );
  }
}
