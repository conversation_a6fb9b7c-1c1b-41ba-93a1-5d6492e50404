import { NextRequest, NextResponse } from "next/server";
import { s3 } from "@/lib/s3";
import { GetObjectCommand } from "@aws-sdk/client-s3";

export async function GET(request: NextRequest) {
  try {
    // Extrair o caminho da URL
    const pathname = request.nextUrl.pathname;
    const path = pathname.replace('/api/images/', '').split('/');
    
    const key = path.join('/');
    console.log('Tentando buscar imagem com key:', key);

    const command = new GetObjectCommand({
      Bucket: "uploads",
      Key: key,
    });

    const response = await s3.send(command);
    console.log('Imagem encontrada, content-type:', response.ContentType);
    
    const stream = response.Body as ReadableStream;
    
    return new NextResponse(stream, {
      headers: {
        'Content-Type': response.ContentType || 'image/jpeg',
        'Cache-Control': 'public, max-age=31536000', // Cache por 1 ano
      },
    });
  } catch (error) {
    console.error('Erro ao buscar imagem:', error);
    return new NextResponse(null, { status: 404 });
  }
} 