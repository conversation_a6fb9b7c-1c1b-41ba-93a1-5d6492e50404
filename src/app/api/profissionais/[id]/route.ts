import { NextResponse } from "next/server";
import { db } from "@/lib/db";

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function GET(request: Request, context: RouteParams) {
  try {
    const params = await context.params;
    const dentist = await db.user.findUnique({
      where: {
        id: params.id,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        image: true,
        latitude: true,
        longitude: true,
        urgency: true,
        city: true,
        state: true,
        whatsapp: true,
      },
    });

    if (!dentist) {
      return NextResponse.json(
        { error: "Profissional não encontrado" },
        { status: 404 }
      );
    }

    // Transformar os dados para o formato esperado pelo frontend
    const formattedDentist = {
      id: dentist.id,
      nome: `${dentist.firstName} ${dentist.lastName}`,
      foto: dentist.image || "/assets/img/default-avatar.png",
      latitude: dentist.latitude || -31.722998,
      longitude: dentist.longitude || -52.358097,
      urgencia: dentist.urgency ? [dentist.urgency] : [],
      cidade: dentist.city,
      uf: dentist.state,
      telefone: dentist.whatsapp,
    };

    return NextResponse.json(formattedDentist);
  } catch (error) {
    console.error("Erro ao buscar profissional:", error);
    return NextResponse.json(
      { error: "Erro ao buscar profissional" },
      { status: 500 }
    );
  }
}
