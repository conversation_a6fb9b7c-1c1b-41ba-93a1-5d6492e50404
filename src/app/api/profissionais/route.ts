import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const nome = searchParams.get("nome");
    const sobrenome = searchParams.get("sobrenome");
    const especialidade = searchParams.get("especialidade");
    const cidade = searchParams.get("cidade");
    const estado = searchParams.get("estado");
    const urgencia = searchParams.get("urgencia");
    const clinicaGeral = searchParams.get("clinicaGeral");
    const considerarAlteracoes = searchParams.get("considerarAlteracoes");

    const where: any = {
      type: "dentist",
      active: true,
    };

    if (nome) {
      where.firstName = {
        contains: nome,
        mode: "insensitive",
      };
    }

    if (sobrenome) {
      where.lastName = {
        contains: sobrenome,
        mode: "insensitive",
      };
    }

    if (especialidade && especialidade !== "all") {
      where.specialties = {
        some: {
          specialtyId: especialidade,
        },
      };
    }

    if (cidade) {
      where.city = {
        contains: cidade,
        mode: "insensitive",
      };
    }

    if (estado && estado !== "all") {
      where.state = estado.toUpperCase();
    }

    if (urgencia === "true") {
      where.urgency = {
        not: null,
      };
    }

    if (clinicaGeral === "true") {
      where.specialties = {
        some: {
          specialtyId: "CLINICO_GERAL",
        },
      };
    }

    if (considerarAlteracoes === "true") {
      const session = await getServerSession(authOptions);
      if (session?.user) {
        const userId = (session.user as any).id;
        const userChanges = await db.userChange.findMany({
          where: { userId },
          select: { changeId: true },
        });

        if (userChanges.length > 0) {
          const changeIds = userChanges.map((change) => change.changeId);
          where.profissionalChanges = {
            some: {
              changeId: {
                in: changeIds,
              },
            },
          };
        }
      }
    }

    const profissionais = await db.user.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        image: true,
        latitude: true,
        longitude: true,
        specialties: {
          select: {
            specialtyId: true,
          },
        },
        urgency: true,
        city: true,
        state: true,
      },
    });

    console.log("Profissionais encontrados:", profissionais);

    // Transformar os dados para o formato esperado pelo frontend
    const formattedProfissionais = profissionais.map((profissional) => ({
      id: profissional.id,
      nome: `${profissional.firstName} ${profissional.lastName}`,
      foto: profissional.image || "/assets/img/default-avatar.png",
      latitude: profissional.latitude || -31.722998,
      longitude: profissional.longitude || -52.358097,
      especialidades: profissional.specialties.map((s) => s.specialtyId),
      urgencia: profissional.urgency ? [profissional.urgency] : [],
      cidade: profissional.city,
      uf: profissional.state,
    }));

    return NextResponse.json(formattedProfissionais);
  } catch (error) {
    console.error("Erro ao buscar profissionais:", error);
    return NextResponse.json(
      { error: "Erro ao buscar profissionais" },
      { status: 500 }
    );
  }
}
