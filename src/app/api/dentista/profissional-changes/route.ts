import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { Prisma } from "@prisma/client";

interface ProfessionalChange {
  changeId: number;
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { changes } = await request.json();
    const userId = (session.user as any).id;

    // Usando transação para garantir consistência
    const result = await db.$transaction(
      async (tx: Prisma.TransactionClient) => {
        // Remove alterações antigas
        await tx.profissionalChange.deleteMany({
          where: {
            userId: userId,
          },
        });

        // Cria novas alterações
        return Promise.all(
          changes.map((change: ProfessionalChange) =>
            tx.profissionalChange.create({
              data: {
                userId: userId,
                changeId: change.changeId,
              },
            })
          )
        );
      }
    );

    return NextResponse.json({ message: "Alterações salvas com sucesso" });
  } catch (error) {
    console.error("Erro ao salvar alterações:", error);
    return NextResponse.json(
      { error: "Erro ao salvar alterações" },
      { status: 500 }
    );
  }
}
