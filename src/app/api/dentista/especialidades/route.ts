import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  getDentistaEspecialidades,
  updateDentistaEspecialidades,
} from "@/app/actions/queries";

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const userId = (session.user as any).id;
    const [userUrgency, userSpecialties] = await getDentistaEspecialidades(
      userId
    );

    return NextResponse.json({
      especialidades: userSpecialties.map((spec) => spec.specialtyId),
      urgencia: userUrgency?.urgency || "",
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao carregar especialidades" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { especialidades, urgencia } = await request.json();
    const userId = (session.user as any).id;

    const result = await updateDentistaEspecialidades(
      userId,
      especialidades,
      urgencia
    );
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { error: "Erro ao atualizar especialidades" },
      { status: 500 }
    );
  }
}
