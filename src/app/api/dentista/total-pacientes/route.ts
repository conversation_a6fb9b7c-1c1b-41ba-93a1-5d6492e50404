import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Busca o estado do dentista logado
    const dentista = await db.user.findUnique({
      where: { id: (session.user as any).id },
      select: { state: true },
    });

    if (!dentista?.state) {
      return NextResponse.json({ total: 0 });
    }

    // Conta pacientes do mesmo estado
    const total = await db.user.count({
      where: {
        type: "patient",
        state: dentista.state,
      },
    });

    return NextResponse.json({ total });
  } catch (error) {
    console.error("Erro ao buscar total de pacientes:", error);
    return NextResponse.json(
      { error: "Erro ao buscar total de pacientes" },
      { status: 500 }
    );
  }
}
