import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import crypto from "crypto";
import { sendEmail } from "@/lib/email";

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    // Gerar token aleatório
    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hora

    // Atualizar usuário com o token (mesmo se não existir, para evitar timing attacks)
    const user = await db.user.findUnique({ where: { email } });

    if (user) {
      await db.user.update({
        where: { email },
        data: { resetToken, resetTokenExpiry },
      });

      // Construir URL de reset
      const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
      const resetUrl = `${baseUrl}/login/reset-password?token=${resetToken}`;

      // Enviar email
      await sendEmail({
        to: email,
        subject: "Recuperação de Senha",
        html: `
          <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h2>Recuperação de Senha</h2>
            <p>Você solicitou a recuperação de senha para sua conta.</p>
            <p>Clique no link abaixo para definir uma nova senha:</p>
            <p><a href="${resetUrl}" style="padding: 10px 15px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px;">Redefinir Senha</a></p>
            <p>Este link expira em 1 hora.</p>
            <p>Se você não solicitou esta recuperação, ignore este email.</p>
          </div>
        `,
      });
    }

    // Sempre retornar a mesma resposta, independente do email existir ou não
    return NextResponse.json({
      success: true,
      message:
        "Se o email estiver cadastrado, enviamos instruções para recuperação de senha.",
    });
  } catch (error) {
    console.error("Erro ao processar recuperação de senha:", error);
    return NextResponse.json(
      { error: "Erro ao processar solicitação" },
      { status: 500 }
    );
  }
}
