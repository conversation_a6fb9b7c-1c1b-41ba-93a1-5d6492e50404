import { NextResponse } from "next/server";
import { db } from "@/lib/db";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const token = url.searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { valid: false, error: "Token não fornecido" },
        { status: 400 }
      );
    }

    const user = await db.user.findFirst({
      where: {
        resetToken: token,
        resetTokenExpiry: { gt: new Date() },
      },
    });

    return NextResponse.json({ valid: !!user });
  } catch (error) {
    console.error("Erro ao verificar token:", error);
    return NextResponse.json(
      { valid: false, error: "Erro ao verificar token" },
      { status: 500 }
    );
  }
}
