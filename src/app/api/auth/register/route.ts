import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { hash } from "bcryptjs";
import { registerSchema } from "@/lib/validations/auth";
import { sendWelcomeEmail } from "@/lib/email/sendWelcomeEmail";

const prisma = new PrismaClient();

export async function POST(req: Request) {
  try {
    const body = await req.json();
    console.log("Dados recebidos no backend:", body); // Log para debug

    // Validar os dados recebidos
    const validationResult = registerSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Dados inválidos", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Verificar se o email já existe
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email já cadastrado" },
        { status: 400 }
      );
    }

    // Criptografar a senha
    const hashedPassword = await hash(data.password, 12);

    // Preparar os dados para salvar
    const userData = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      whatsapp: data.whatsapp,
      zipCode: data.zipCode,
      state: data.state,
      city: data.city,
      hashedPassword,
      type: data.type,
      latitude: data.latitude, // Adicionar explicitamente
      longitude: data.longitude, // Adicionar explicitamente
      addressPrecision: data.addressPrecision, // Adicionar explicitamente
      // Campos específicos para dentista
      ...(data.type === "dentist" && {
        cpf: data.cpf,
        cro: data.cro,
        croState: data.croState,
        address: data.address,
        number: data.number,
        complement: data.complement,
        district: data.district,
      }),
    };

    console.log("Dados preparados para salvar:", userData); // Log para debug

    // Criar o usuário
    const user = await prisma.user.create({
      data: userData,
    });

    console.log("Usuário criado:", user); // Log para debug

    // Envia email de boas-vindas apenas para dentistas e pacientes
    if (data.type === "dentist" || data.type === "patient") {
      await sendWelcomeEmail({
        to: user.email,
        firstName: user.firstName,
        userType: data.type as "dentist" | "patient",
      });
    }

    // Remover a senha do objeto retornado
    const { hashedPassword: _, ...userWithoutPassword } = user;

    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error("Erro ao registrar usuário:", error);
    return NextResponse.json({ error: "Erro ao criar conta" }, { status: 500 });
  }
}
