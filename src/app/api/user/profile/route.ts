import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import * as z from "zod";

// Schema base com campos comuns
const baseProfileSchema = z.object({
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  email: z.string().email(),
  whatsapp: z.string().min(11),
  zipCode: z.string().length(8),
  state: z.string().length(2),
  city: z.string().min(2),
});

// Schema específico para dentistas
const dentistProfileSchema = baseProfileSchema.extend({
  cro: z.string().min(4),
  cpf: z.string().length(11),
  number: z.string().min(1),
  address: z.string().min(5),
  complement: z.string().optional(),
  district: z.string().min(2),
});

// Schema específico para pacientes
const patientProfileSchema = baseProfileSchema;

// Tipos inferidos dos schemas
type BaseProfile = z.infer<typeof baseProfileSchema>;
type DentistProfile = z.infer<typeof dentistProfileSchema>;
type PatientProfile = z.infer<typeof patientProfileSchema>;

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email },
      select: {
        type: true, // Adicionando o campo type
        cro: true,
        cpf: true,
        firstName: true,
        lastName: true,
        email: true,
        whatsapp: true,
        zipCode: true,
        state: true,
        city: true,
        number: true,
        address: true,
        complement: true,
        district: true,
        image: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Usuário não encontrado" },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Erro ao buscar perfil:", error);
    return NextResponse.json(
      { error: "Erro ao buscar perfil" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const body = await req.json();
    console.log("Received body:", body);

    // Buscar o usuário para verificar o tipo
    const user = await db.user.findUnique({
      where: { email: session.user?.email ?? "" },
    });

    if (!user) {
      return NextResponse.json(
        { error: "Usuário não encontrado" },
        { status: 404 }
      );
    }

    if (user.type === "dentist") {
      const validatedData = dentistProfileSchema.parse(body);
      const updatedUser = await db.user.update({
        where: { email: session.user?.email ?? "" },
        data: validatedData,
      });
      return NextResponse.json(updatedUser);
    } else {
      const validatedData = patientProfileSchema.parse(body);
      const updatedUser = await db.user.update({
        where: { email: session.user?.email ?? "" },
        data: validatedData,
      });
      return NextResponse.json(updatedUser);
    }
  } catch (error) {
    console.error("Erro ao atualizar perfil:", error);
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dados inválidos", details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: "Erro ao atualizar perfil" },
      { status: 500 }
    );
  }
}
