import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const userId = (session.user as any).id;

    const userChanges = await db.userChange.findMany({
      where: {
        userId,
      },
      select: {
        changeId: true,
      },
    });

    return NextResponse.json(userChanges);
  } catch (error) {
    console.error("Erro ao buscar alterações do usuário:", error);
    return NextResponse.json(
      { error: "Erro ao buscar alterações do usuário" },
      { status: 500 }
    );
  }
} 