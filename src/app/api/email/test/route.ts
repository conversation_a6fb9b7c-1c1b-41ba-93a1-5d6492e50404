import { NextResponse } from "next/server";
import { sendWelcomeEmail } from "@/lib/email/sendWelcomeEmail";

export async function GET() {
  try {
    // Log das variáveis de ambiente (remova em produção)
    console.log("Verificando configurações SMTP:", {
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      userConfigured: !!process.env.SMTP_USER,
      passConfigured: !!process.env.SMTP_PASS,
    });

    const result = await sendWelcomeEmail({
      to: "<EMAIL>",
      firstName: "Teste",
      userType: "dentist",
    });

    if (!result.success) {
      console.error("Falha ao enviar email:", result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error || "Falha ao enviar email de teste",
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Email de teste enviado com sucesso",
      messageId: result.messageId,
    });
  } catch (error) {
    console.error("Erro detalhado:", error);
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Erro desconhecido ao enviar email",
      },
      { status: 500 }
    );
  }
}
