import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { s3 } from "@/lib/s3";
import { PutObjectCommand, DeleteObjectCommand } from "@aws-sdk/client-s3";
import crypto from "crypto";

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("image") as File;

    if (!file) {
      return NextResponse.json(
        { error: "Nenhuma imagem fornecida" },
        { status: 400 }
      );
    }

    // Gerar um nome único para o arquivo
    const fileExtension = file.name.split(".").pop();
    const fileName = `${crypto.randomUUID()}.${fileExtension}`;
    const key = `dentist-images/${fileName}`;

    // Converter o arquivo para buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    try {
      // Primeiro, vamos buscar a imagem antiga do usuário
      const user = await db.user.findUnique({
        where: { id: (session.user as any).id },
        select: { image: true }
      });

      // Se existir uma imagem antiga, vamos deletá-la do MinIO
      if (user?.image) {
        const oldImageKey = user.image.split('/').slice(-2).join('/'); // Pega apenas a parte "dentist-images/filename.ext"
        const deleteCommand = new DeleteObjectCommand({
          Bucket: "uploads",
          Key: oldImageKey,
        });
        await s3.send(deleteCommand);
      }

      // Upload da nova imagem
      const command = new PutObjectCommand({
        Bucket: "uploads",
        Key: key,
        Body: buffer,
        ContentType: file.type,
        ACL: 'public-read',
      });

      await s3.send(command);

      // Atualiza o imageLink do usuário no banco de dados
      const imageUrl = `/api/images/${key}`;
      await db.user.update({
        where: {
          id: (session.user as any).id,
        },
        data: {
          image: imageUrl,
        },
      });

      return NextResponse.json({
        imageUrl: imageUrl,
        publicId: key,
      });

    } catch (uploadError) {
      console.error("Erro específico no upload para MinIO:", uploadError);
      return NextResponse.json(
        { error: "Erro ao fazer upload para o MinIO", details: uploadError instanceof Error ? uploadError.message : 'Erro desconhecido' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Erro geral no upload:", error);
    return NextResponse.json(
      { error: "Erro ao fazer upload da imagem", details: error instanceof Error ? error.message : 'Erro desconhecido' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user || !(session.user as any).id) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { publicId } = await request.json();

    // Deleta a imagem do MinIO
    const command = new DeleteObjectCommand({
      Bucket: "uploads",
      Key: publicId,
    });

    await s3.send(command);

    // Remove o imageLink do usuário
    await db.user.update({
      where: {
        id: (session.user as any).id,
      },
      data: {
        image: null,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Erro ao deletar imagem:", error);
    return NextResponse.json(
      { error: "Erro ao deletar imagem" },
      { status: 500 }
    );
  }
} 