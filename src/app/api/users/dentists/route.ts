import { NextResponse } from "next/server";
import { db } from "@/lib/db";

interface Coordinates {
  latitude: number;
  longitude: number;
}

interface DentistFromDB {
  id: string;
  firstName: string;
  lastName: string;
  image: string | null;
  zipCode: string;
  specialties: Array<{ specialtyId: string }>;
  urgency: string | null;
  city: string | null;
  state: string | null;
}

interface FormattedDentist {
  id: string;
  nome: string;
  foto: string;
  latitude: number;
  longitude: number;
  especialidades: string[];
  urgencia: string[];
  cidade: string | null;
  uf: string | null;
}

async function getCoordinatesFromZipCode(zipCode: string): Promise<Coordinates> {
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${zipCode}&key=${process.env.GOOGLE_MAPS_API_KEY}`
    );
    const data = await response.json();

    if (data.results && data.results[0]) {
      const { lat, lng } = data.results[0].geometry.location;
      return { latitude: lat, longitude: lng };
    }

    // Coordenadas padrão para Pelotas-RS caso não encontre
    return { latitude: -31.720998973000878, longitude: -52.35809723173391 };
  } catch (error) {
    console.error("Erro ao buscar coordenadas:", error);
    // Coordenadas padrão para Pelotas-RS em caso de erro
    return { latitude: -31.720998973000878, longitude: -52.35809723173391 };
  }
}

export async function GET() {
  try {
    const dentists = await db.user.findMany({
      where: {
        type: "dentist",
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        image: true,
        zipCode: true,
        specialties: {
          select: {
            specialtyId: true,
          },
        },
        urgency: true,
        city: true,
        state: true,
      },
    }) as DentistFromDB[];

    // Busca as coordenadas para cada dentista
    const dentistsWithCoordinates = await Promise.all(
      dentists.map(async (dentist: DentistFromDB): Promise<FormattedDentist> => {
        const coordinates = await getCoordinatesFromZipCode(dentist.zipCode);

        return {
          id: dentist.id,
          nome: `${dentist.firstName} ${dentist.lastName}`,
          foto: dentist.image || "/assets/img/robo-feliz.png",
          latitude: coordinates.latitude,
          longitude: coordinates.longitude,
          especialidades: dentist.specialties.map((s) => s.specialtyId),
          urgencia: dentist.urgency ? [dentist.urgency] : [],
          cidade: dentist.city,
          uf: dentist.state,
        };
      })
    );

    return NextResponse.json(dentistsWithCoordinates);
  } catch (error) {
    console.error("Erro ao buscar dentistas:", error);
    return NextResponse.json(
      { error: "Erro ao buscar dentistas" },
      { status: 500 }
    );
  }
}
