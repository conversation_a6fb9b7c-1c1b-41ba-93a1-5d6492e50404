<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="428" height="926" viewBox="0 0 428 926">
  <defs>
    <clipPath id="clip-path">
      <rect id="Retângulo_4" data-name="Retângulo 4" width="428" height="926" fill="#fff"/>
    </clipPath>
  </defs>
  <g id="Grupo_de_máscara_2" data-name="Grupo de máscara 2" clip-path="url(#clip-path)">
    <path id="_18" data-name="18" d="M629.726,178.136c2.347,27.734-4.015,55.1-13.527,80.979-10.315,27.919-23.966,54.418-34.961,82.029a277.855,277.855,0,0,0-13.589,42.991c-3.4,15.689-4.386,31.749-7.1,47.5-4.942,28.352-18.531,51.824-43.114,67.451-24.646,15.627-53.8,21.125-82.646,20.137-38.544-1.359-78.137-11.674-115.754-18.716-39.655-7.412-78.878-2.9-117.546,8.03-53.553,15.133-123.6,7.35-151.58-47.871-14.824-29.278-10.933-63.189-11.118-94.815A205.215,205.215,0,0,0,31.5,311.433c-5.127-18.345-12.168-36.135-18.283-54.171C3.516,228.663-4.2,197.408,2.528,167.2c6.362-28.6,26.622-50.033,49.847-66.648C109.82,59.541,178.939,31.93,247.317,15.808,365.356-12.049,522.99-13.346,599.891,98.022,616.2,121.742,627.317,149.29,629.726,178.136Z" transform="translate(790.853 800.36) rotate(135)" fill="#2560d6"/>
    <path id="_27" data-name="27" d="M844.079,529.439c14.008-29.693,16.357-65.174,1.426-94.113-21.976-42.778-74.736-61.567-122.8-59.974-98.474,3.271-165.662,67.439-257.845,89.751-55.444,13.421-118.186,8.723-163.481-26-38.585-29.526-59.974-77.253-67.02-125.316-11.156-75.995,8.22-149.808-10.653-225.719-8.3-33.384-27.345-68.529-60.561-82.453C127.582-9.234,91.011,6.955,65.763,33.041c-28.519,29.526-44.2,68.278-54.018,107.533C-9.728,226.3-1.424,318.818,34.141,399.594,64.589,468.71,125.9,521.973,191.414,556.532c84.718,44.708,178.663,71.717,273.95,80.1,83.711,7.381,180.424-2.1,262.039-22.564,39.843-9.982,78.511-30.029,103.423-62.742A128.905,128.905,0,0,0,844.079,529.439Z" transform="translate(-125.695 422.609)" fill="#227fe6"/>
    <path id="_18-2" data-name="18" d="M629.726,178.136c2.347,27.734-4.015,55.1-13.527,80.979-10.315,27.919-23.966,54.418-34.961,82.029a277.855,277.855,0,0,0-13.589,42.991c-3.4,15.689-4.386,31.749-7.1,47.5-4.942,28.352-18.531,51.824-43.114,67.451-24.646,15.627-53.8,21.125-82.646,20.137-38.544-1.359-78.137-11.674-115.754-18.716-39.655-7.412-78.878-2.9-117.546,8.03-53.553,15.133-123.6,7.35-151.58-47.871-14.824-29.278-10.933-63.189-11.118-94.815A205.215,205.215,0,0,0,31.5,311.433c-5.127-18.345-12.168-36.135-18.283-54.171C3.516,228.663-4.2,197.408,2.528,167.2c6.362-28.6,26.622-50.033,49.847-66.648C109.82,59.541,178.939,31.93,247.317,15.808,365.356-12.049,522.99-13.346,599.891,98.022,616.2,121.742,627.317,149.29,629.726,178.136Z" transform="translate(760.853 830.36) rotate(135)" fill="none" stroke="#6aadef" stroke-width="2"/>
    <path id="_29" data-name="29" d="M53.251,27.141A1.754,1.754,0,0,1,56,24.968C57.387,26.737,54.636,28.911,53.251,27.141Zm-16.445,1.77-.212-.269a1.753,1.753,0,0,0-2.75,2.173l.212.269a1.753,1.753,0,0,0,2.75-2.173ZM17.976,42.74a1.783,1.783,0,0,0-.365-2.443c-.308-.212-.615-.4-.923-.615A1.746,1.746,0,1,0,14.61,42.49c.308.212.615.4.923.615A1.75,1.75,0,0,0,17.976,42.74Zm49.7,9.386a1.781,1.781,0,0,0-1.981-1.5c-.25.058-.519.1-.769.154a1.757,1.757,0,0,0,.481,3.481c.25-.058.519-.1.769-.154A1.8,1.8,0,0,0,67.677,52.126ZM47.116,53.357l-.212-.269a1.753,1.753,0,1,0-2.75,2.173l.212.269a1.784,1.784,0,0,0,2.462.289A1.8,1.8,0,0,0,47.116,53.357ZM46.673.637A1.784,1.784,0,0,0,44.211.348L43.942.56a1.784,1.784,0,0,0-.289,2.462,1.784,1.784,0,0,0,2.462.289l.269-.212A1.781,1.781,0,0,0,46.673.637ZM20.842,1.791a1.754,1.754,0,0,0-2.75,2.173C19.476,5.734,22.227,3.56,20.842,1.791ZM3.127,18.39a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,3.127,18.39ZM7.282,62.532a1.785,1.785,0,0,0-.981-2.27c-.231-.115-.481-.212-.712-.327A1.748,1.748,0,1,0,4.3,63.186c.231.115.481.212.712.327A1.736,1.736,0,0,0,7.282,62.532Zm18.33,3.712a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,25.612,66.244ZM53.367,78.3a1.754,1.754,0,0,0-2.75,2.173C52,82.247,54.752,80.073,53.367,78.3ZM30.151,90.748a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,30.151,90.748ZM13.764,81.8a1.754,1.754,0,0,0-2.75,2.173C12.4,85.748,15.149,83.574,13.764,81.8Zm59.087,6.944c.212-.308.4-.615.615-.923a1.746,1.746,0,0,0-2.808-2.077c-.212.308-.4.615-.615.923a1.746,1.746,0,1,0,2.808,2.077Zm-6.559,19.061-.212-.269a1.753,1.753,0,1,0-2.75,2.173l.212.269a1.753,1.753,0,1,0,2.75-2.173Zm-25.351,1.443a1.776,1.776,0,0,0-1.539-1.943l-.5-.058a1.752,1.752,0,0,0-.4,3.481l.5.058A1.776,1.776,0,0,0,40.942,109.252ZM9.994,107.059l-.212-.269a1.753,1.753,0,0,0-2.75,2.173l.212.269a1.784,1.784,0,0,0,2.462.289A1.8,1.8,0,0,0,9.994,107.059ZM29.67,128.37c.019-.173.038-.327.058-.5a1.752,1.752,0,1,0-3.481-.4l-.058.5a1.752,1.752,0,0,0,3.481.4ZM13.841,143.7a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,13.841,143.7Zm23.408,8.79a1.784,1.784,0,0,0-2.462-.289l-.269.212a1.753,1.753,0,1,0,2.173,2.75l.269-.212A1.786,1.786,0,0,0,37.249,152.49Zm31.313-28.2a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,68.562,124.293ZM52.771,135.371a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,52.771,135.371Zm12.983,18.772A1.754,1.754,0,0,0,63,156.317,1.754,1.754,0,0,0,65.754,154.144ZM52.828,174.57a1.754,1.754,0,0,0-2.75,2.173A1.754,1.754,0,0,0,52.828,174.57Zm-30.178-10.6-.442-.558a1.753,1.753,0,0,0-2.75,2.173l.442.558a1.784,1.784,0,0,0,2.462.289A1.8,1.8,0,0,0,22.65,163.972Z" transform="matrix(0.899, -0.438, 0.438, 0.899, 322.173, -0.412)" fill="#fff" opacity="0.327"/>
  </g>
</svg>
