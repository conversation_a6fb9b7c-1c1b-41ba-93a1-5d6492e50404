 # Database Configuration
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_DB=
POSTGRES_PORT=5432
POSTGRES_EXTERNAL_PORT=5432

# Application Configuration
APP_PORT=3000
APP_EXTERNAL_PORT=3000

# MinIO Configuration
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_API_PORT=9000
MINIO_API_EXTERNAL_PORT=8000
MINIO_CONSOLE_PORT=9001
MINIO_CONSOLE_EXTERNAL_PORT=9001
MINIO_ENDPOINT=ceudaboca-minio

SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=yybt qned hrcx ctsd