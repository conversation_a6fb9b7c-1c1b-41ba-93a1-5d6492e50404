# Build stage
FROM node:18-alpine AS builder
WORKDIR /app

# Instala dependências do sistema necessárias
RUN apk add --no-cache libc6-compat openssl

# Copia arquivos de configuração
COPY package*.json ./
COPY prisma ./prisma/
COPY .env .env

# Instala dependências
RUN npm ci

# Copia código fonte
COPY . .

# Gera Prisma Client
RUN npx prisma generate

# Build da aplicação
RUN npm run build

# Production stage
FROM node:18-alpine AS runner
WORKDIR /app

# Instala dependências do sistema necessárias
RUN apk add --no-cache libc6-compat openssl

# Cria usuário não-root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copia build e arquivos necessários
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/package.json ./package.json

# Instala apenas as dependências necessárias para o seed
RUN npm install --production=false typescript ts-node @types/node @prisma/client bcryptjs

# Define usuário não-root
USER nextjs

# Expõe porta da aplicação
EXPOSE 3000

# Comando para iniciar a aplicação
CMD ["node", "server.js"]
