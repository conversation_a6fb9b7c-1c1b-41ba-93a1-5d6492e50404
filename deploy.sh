#!/bin/bash

# Verifica se o arquivo .env existe
if [ ! -f .env ]; then
    echo "Arquivo .env não encontrado."
    exit 1
fi

# Carrega variáveis do .env
set -a
source .env
set +a

echo "🚀 Iniciando deploy..."

# Para os containers existentes
echo "📥 Parando containers existentes..."
docker-compose down

# Remove imagens antigas
echo "🗑️ Removendo imagens antigas..."
docker-compose rm -f

# Constrói as novas imagens
echo "🏗️ Construindo novas imagens..."
docker-compose build --no-cache

# Inicia os containers
echo "📦 Iniciando containers..."
docker-compose up -d

# Espera o postgres estar pronto
echo "⏳ Aguardando PostgreSQL inicializar..."
sleep 10

# Executa as migrations e seed
echo "🔄 Executando migrations e seed..."
docker-compose exec app sh -c "echo 'y' | npx prisma migrate deploy && node prisma/seed.ts"

echo "✅ Deploy concluído com sucesso!"
echo "📊 PostgreSQL está rodando na porta: $POSTGRES_EXTERNAL_PORT"
echo "🌐 Aplicação está rodando na porta: $APP_EXTERNAL_PORT"
