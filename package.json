{"name": "app-ceu-da-boca", "version": "0.1.0", "private": true, "prisma": {"seed": "node prisma/seed.ts"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "ts-node scripts/create-admin.ts", "create-user": "ts-node --project tsconfig-scripts.json scripts/create-user.ts", "seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.804.0", "@capacitor/cli": "^7.1.0", "@capacitor/core": "^7.1.0", "@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@react-google-maps/api": "^2.20.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.0.0", "lucide-react": "^0.484.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-input-mask": "^2.0.4", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20.17.30", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-input-mask": "^3.0.6", "prisma": "^6.5.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}