services:
  postgres:
    env_file: .env
    image: postgres:17
    container_name: ceudaboca-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_EXTERNAL_PORT}:${POSTGRES_PORT}"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  app:
    env_file: .env
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ceudaboca-app
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@ceudaboca-postgres:${POSTGRES_PORT}/${POSTGRES_DB}
      - PORT=${APP_PORT}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_API_PORT=${MINIO_API_PORT}
      - MINIO_ROOT_USER=${MINIO_ROOT_USER}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
    ports:
      - "${APP_EXTERNAL_PORT}:${APP_PORT}"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  minio:
    env_file: .env
    image: minio/minio
    container_name: ceudaboca-minio
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server --console-address ":9001" /data
    ports:
      - "${MINIO_API_EXTERNAL_PORT}:${MINIO_API_PORT}"
      - "${MINIO_CONSOLE_EXTERNAL_PORT}:${MINIO_CONSOLE_PORT}"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  postgres_data:
  minio_data:
