@echo off
chcp 65001 >nul

:: Verifica se o arquivo .env existe
if not exist .env (
    echo Arquivo .env não encontrado.
    exit /b 1
)

:: Carrega variáveis do .env
for /f "tokens=*" %%a in (.env) do (
    set %%a
)

echo 🚀 Iniciando deploy...

:: Limpa recursos Docker não utilizados
echo 🧹 Limpando recursos Docker não utilizados...
docker system prune -f

:: Para os containers existentes
echo 📥 Parando containers existentes...
docker-compose down

:: Remove imagens antigas
echo 🗑️ Removendo imagens antigas...
docker-compose rm -f

:: Constrói as novas imagens
echo 🏗️ Construindo novas imagens...
docker-compose build --no-cache
if %ERRORLEVEL% neq 0 (
    echo ❌ Erro ao construir imagens. Abortando deploy.
    exit /b %ERRORLEVEL%
)

:: Inicia os containers
echo 📦 Iniciando containers...
docker-compose up -d
if %ERRORLEVEL% neq 0 (
    echo ❌ Erro ao iniciar containers. Abortando deploy.
    exit /b %ERRORLEVEL%
)

:: Espera o postgres estar pronto
echo ⏳ Aguardando PostgreSQL inicializar...
timeout /t 10 /nobreak >nul

:: Verifica se o container app está rodando
docker-compose ps app | findstr "Up" >nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Container app não está rodando. Abortando deploy.
    exit /b 1
)

:: Executa as migrations e seed
echo 🔄 Executando migrations e seed...
docker-compose exec app sh -c "echo 'y' | npx prisma migrate deploy && node prisma/seed.ts"

echo ✅ Deploy concluído com sucesso!
echo 📊 PostgreSQL está rodando na porta: %POSTGRES_EXTERNAL_PORT%
echo 🌐 Aplicação está rodando na porta: %APP_EXTERNAL_PORT%
