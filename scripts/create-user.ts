import { PrismaClient } from "@prisma/client";
import { hash } from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  // Primeiro, vamos deletar o usuário se ele já existir
  await prisma.user.deleteMany({
    where: {
      email: "<EMAIL>",
    },
  });

  // Criar a senha hash
  const hashedPassword = await hash("password123", 12);

  // Criar novo usuário
  const user = await prisma.user.create({
    data: {
      email: "<EMAIL>",
      hashedPassword,
      firstName: "Admin",
      lastName: "Test",
      type: "admin",
      whatsapp: "11999999999",
      zipCode: "12345678",
      state: "SP",
      city: "São Paulo",
    },
  });

  console.log("User created:", user);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
