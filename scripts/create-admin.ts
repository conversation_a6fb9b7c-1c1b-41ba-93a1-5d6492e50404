import { PrismaClient } from "@prisma/client";
import { hash } from "bcryptjs";

const prisma = new PrismaClient();

async function createInitialAdmin() {
  try {
    const hashedPassword = await hash("admin", 10);

    const admin = await prisma.user.create({
      data: {
        firstName: "Admin",
        lastName: "System",
        email: "<EMAIL>",
        hashedPassword,
        type: "admin",
        // Campos obrigatórios adicionais
        whatsapp: "00000000000",
        zipCode: "00000000",
        state: "SP",
        city: "São Paulo",
      },
    });

    console.log("Admin inicial criado com sucesso:", admin);
  } catch (error) {
    console.error("Erro ao criar admin inicial:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createInitialAdmin();
