// prisma/seed.ts
const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");

const prismaClient = new PrismaClient();

async function main() {
  // Verifica se já existe um admin
  const existingAdmin = await prismaClient.user.findFirst({
    where: {
      type: "admin",
      email: "<EMAIL>",
    },
  });

  if (existingAdmin) {
    console.log("Admin já existe, pulando criação...");
    return;
  }

  const hashedPassword = await bcrypt.hash("admin123", 10);

  await prismaClient.user.create({
    data: {
      firstName: "Admin",
      lastName: "System",
      email: "<EMAIL>",
      hashedPassword,
      type: "admin",
      whatsapp: "00000000000",
      zipCode: "00000000",
      state: "SP",
      city: "São Paulo",
    },
  });

  console.log("✅ Admin criado com sucesso!");
}

main()
  .catch((e) => {
    console.error("❌ Erro ao executar seed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prismaClient.$disconnect();
  });
