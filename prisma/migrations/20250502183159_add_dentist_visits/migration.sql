-- CreateTable
CREATE TABLE "DentistVisit" (
    "id" TEXT NOT NULL,
    "dentistId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "visitedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DentistVisit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "DentistVisit_dentistId_patientId_key" ON "DentistVisit"("dentistId", "patientId");

-- AddForeignKey
ALTER TABLE "DentistVisit" ADD CONSTRAINT "DentistVisit_dentistId_fkey" FOREIGN KEY ("dentistId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DentistVisit" ADD CONSTRAINT "DentistVisit_patientId_fkey" FOREI<PERSON><PERSON> KEY ("patientId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
