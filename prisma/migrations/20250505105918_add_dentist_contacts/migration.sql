-- CreateTable
CREATE TABLE "DentistContact" (
    "id" TEXT NOT NULL,
    "dentistId" TEXT NOT NULL,
    "patientId" TEXT NOT NULL,
    "contactedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DentistContact_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "DentistContact_dentistId_patientId_key" ON "DentistContact"("dentistId", "patientId");

-- AddForeignKey
ALTER TABLE "DentistContact" ADD CONSTRAINT "DentistContact_dentistId_fkey" FOREIGN KEY ("dentistId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DentistContact" ADD CONSTRAINT "DentistContact_patientId_fkey" FOREI<PERSON><PERSON> KEY ("patientId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
