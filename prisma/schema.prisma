// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String    @id @default(cuid())
  firstName      String
  lastName       String
  cpf            String?   
  email          String    @unique
  whatsapp       String
  zipCode        String
  state          String
  city           String
  hashedPassword String
  type           String
  visits         Int       @default(0)
  cro            String?   
  croState       String?   
  address        String?   
  number         String?   
  complement     String?   
  district       String?   
  image          String?   
  urgency        String?
  latitude       Float?
  longitude      Float?
  addressPrecision String?
  imported       Boolean   @default(false)
  active         Boolean   @default(true)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  UserChange     UserChange[]
  specialties    UserSpecialty[]
  profissionalChanges ProfissionalChange[]
  dentistVisits DentistVisit[] @relation("DentistVisits")
  patientVisits DentistVisit[] @relation("PatientVisits")
  dentistContacts DentistContact[] @relation("DentistContacts")
  patientContacts DentistContact[] @relation("PatientContacts")
  resetToken       String?   @db.Text
  resetTokenExpiry DateTime?
}

model UserChange {
  id        Int      @id @default(autoincrement())
  userId    String
  changeId  Int
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@unique([userId, changeId])
}

model UserSpecialty {
  id          Int      @id @default(autoincrement())
  userId      String
  specialtyId String
  user        User     @relation(fields: [userId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([userId, specialtyId])
}

model ProfissionalChange {
  id        Int      @id @default(autoincrement())
  userId    String
  changeId  Int
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@map("profissional_changes")
}

enum TemplateKey {
  WELCOME_EMAIL
  PASSWORD_RESET
  APPOINTMENT_CONFIRMATION
  APPOINTMENT_REMINDER
  APPOINTMENT_CANCELLATION
}

model EmailTemplate {
  id        String      @id @default(cuid())
  key       TemplateKey
  title     String
  template  String      @db.Text
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  campaigns Campaign[]  // Add this line to establish the reverse relation
}

enum CampaignType {
  PATIENT
  PROFESSIONAL
}

model Campaign {
  id                   String       @id @default(cuid())
  name                 String
  type                 CampaignType
  emailTemplate        EmailTemplate @relation(fields: [emailTemplateId], references: [id])
  emailTemplateId      String
  scheduled            Boolean      @default(false)
  lastSentAt           DateTime?
  createdAt            DateTime     @default(now())
  updatedAt            DateTime     @updatedAt
  filterState          String?      
  filterAlteracoes     Int[]        
  filterEspecialidades String[]     // Novo campo para armazenar IDs das especialidades
}

model Specialty {
  id        String    @id @default(cuid())
  name      String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Urgency {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model DentistVisit {
  id        String   @id @default(cuid())
  dentistId String
  patientId String
  visitedAt DateTime @default(now())
  dentist   User     @relation("DentistVisits", fields: [dentistId], references: [id])
  patient   User     @relation("PatientVisits", fields: [patientId], references: [id])

  @@unique([dentistId, patientId])
}

model DentistContact {
  id        String   @id @default(cuid())
  dentistId String
  patientId String
  contactedAt DateTime @default(now())
  dentist   User     @relation("DentistContacts", fields: [dentistId], references: [id])
  patient   User     @relation("PatientContacts", fields: [patientId], references: [id])

  @@unique([dentistId, patientId])
}






